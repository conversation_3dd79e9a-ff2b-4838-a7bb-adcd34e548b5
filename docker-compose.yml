version: '3.8'

services:
  ocr-service:
    build: .
    container_name: ocr-restful-service
    ports:
      - "9527:9527"
    environment:
      - PYTHONPATH=/app
      - LD_PRELOAD=/usr/lib/aarch64-linux-gnu/libgomp.so.1
      - ASCEND_RT_VISIBLE_DEVICES=0,1,2,3,4,5,6,7
    volumes:
      - ./output:/app/output
      - ./temp:/app/temp
      - /usr/local/Ascend/driver:/usr/local/Ascend/driver
      - /usr/local/bin/npu-smi:/usr/local/bin/npu-smi
      - /usr/local/dcmi:/usr/local/dcmi
    privileged: true
    network_mode: host
    shm_size: 128G
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9527/health"]
      interval: 30s
      timeout: 10s
      retries: 3 