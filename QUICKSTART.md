# OCR RESTful Service 快速开始

## 🚀 5分钟快速部署

### 1. 环境检查

确保您的环境满足以下要求：

```bash
# 检查Docker
docker --version

# 检查NPU环境（如果有NPU设备）
npu-smi info
```

### 2. 一键部署

```bash
# 克隆项目
git clone <repository-url>
cd ocrshiyan

# 构建镜像
./scripts/build.sh

# 启动服务
./scripts/run.sh start
```

### 3. 验证服务

```bash
# 检查服务状态
curl http://localhost:9527/health

# 运行测试
python test_client.py
```

## 📖 使用示例

### 1. 图片OCR识别

```bash
# 使用cURL
curl -X POST "http://localhost:9527/ocr" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@your-image.jpg"
```

### 2. PDF OCR识别

```bash
# 使用cURL
curl -X POST "http://localhost:9527/ocr" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@your-document.pdf"
```

### 3. Python客户端

```python
import requests

def ocr_recognize(file_path):
    url = "http://localhost:9527/ocr"
    
    with open(file_path, 'rb') as f:
        files = {'file': f}
        response = requests.post(url, files=files)
    
    return response.json()

# 使用示例
result = ocr_recognize("test.jpg")
print(result)
```

## 🔧 常用命令

```bash
# 启动服务
./scripts/run.sh start

# 停止服务
./scripts/run.sh stop

# 重启服务
./scripts/run.sh restart

# 查看状态
./scripts/run.sh status

# 查看日志
./scripts/run.sh logs

# 构建镜像
./scripts/run.sh build

# 清理资源
./scripts/run.sh clean
```

## 📊 API接口

### 健康检查
```http
GET /health
```

### OCR识别
```http
POST /ocr
Content-Type: multipart/form-data
```

### 服务信息
```http
GET /info
```

### API文档
访问: http://localhost:9527/docs

## 🐛 常见问题

### Q: 服务启动失败怎么办？
A: 检查Docker是否运行，端口9527是否被占用

### Q: NPU设备不可用怎么办？
A: 服务会自动回退到CPU模式，不影响功能

### Q: 文件上传失败怎么办？
A: 检查文件大小是否超过50MB限制

### Q: OCR识别效果不好怎么办？
A: 确保图片清晰，文字对比度足够

## 📞 获取帮助

- 查看详细文档: [README.md](README.md)
- 部署指南: [DEPLOYMENT.md](DEPLOYMENT.md)
- 提交Issue: GitHub Issues

---

**提示**: 首次启动可能需要几分钟时间下载模型文件，请耐心等待。 