# OCR RESTful Service

基于飞桨PaddleOCR的文本识别RESTful服务，支持NPU加速。

## 项目概述

本项目提供了一个完整的OCR文本识别服务，具有以下特性：

- 🚀 **NPU加速**: 基于昇腾NPU硬件加速，提供高性能文本识别
- 📄 **多格式支持**: 支持图片(jpg, jpeg, png, bmp)和PDF文件
- 🔧 **RESTful API**: 提供标准的RESTful接口
- 🐳 **Docker部署**: 完整的Docker容器化部署方案
- 📊 **健康检查**: 内置健康检查和监控功能
- 🔄 **异步处理**: 支持异步文件处理和OCR识别
- 🚫 **离线部署**: 内置PaddleOCR模型文件，支持无网环境部署

## 技术栈

- **后端框架**: FastAPI
- **OCR引擎**: PaddleOCR 3.1.0
- **硬件加速**: 昇腾NPU
- **容器化**: Docker
- **Python版本**: 3.8+

## 项目结构

```
ocrshiyan/
├── Dockerfile                 # 基于官方NPU环境的Dockerfile
├── docker-compose.yml         # Docker编排文件
├── requirements.txt           # Python依赖
├── .paddlex/                 # PaddleOCR模型文件（离线部署）
│   └── official_models/
│       ├── PP-OCRv5_server_det/  # 检测模型
│       └── PP-OCRv5_server_rec/  # 识别模型
├── app/
│   ├── __init__.py
│   ├── main.py               # FastAPI主应用
│   ├── ocr_service.py        # OCR服务核心逻辑
│   └── utils.py              # 工具函数
├── config/
│   └── settings.py           # 配置文件
├── scripts/
│   ├── build.sh              # 构建脚本
│   └── run_arm.sh            # ARM架构运行脚本
└── README.md                 # 项目说明文档
```

## 快速开始

### 1. 环境要求

- Docker 20.10+
- ARM64架构服务器
- 昇腾NPU环境（可选，支持CPU回退）
- 至少4GB内存
- CANN 8.0+

### 2. 构建镜像

```bash
# 使用ARM专用构建脚本
chmod +x scripts/build.sh
./scripts/build.sh

# 或直接使用Docker命令
docker build --platform linux/arm64 -t ocr-restful-service:1.0.0 .
```

### 3. 运行服务

```bash
# 使用ARM专用运行脚本
chmod +x scripts/run_arm.sh
./scripts/run_arm.sh start

# 或使用docker-compose
docker-compose up -d

# 或直接使用Docker命令（推荐用于ARM服务器）
docker run -d \
    --name ocr-restful-service \
    --privileged \
    --network=host \
    --shm-size=128G \
    -e ASCEND_RT_VISIBLE_DEVICES="0,1,2,3,4,5,6,7" \
    -v $(pwd)/output:/app/output \
    -v $(pwd)/temp:/app/temp \
    -v /usr/local/Ascend/driver:/usr/local/Ascend/driver \
    -v /usr/local/bin/npu-smi:/usr/local/bin/npu-smi \
    -v /usr/local/dcmi:/usr/local/dcmi \
    ocr-restful-service:1.0.0
```

### 4. 验证服务

```bash
# 检查服务状态
curl http://localhost:9527/

# 查看健康状态
curl http://localhost:9527/health

# 查看API文档
# 浏览器访问: http://localhost:9527/docs
```

## API接口

### 1. 健康检查

```http
GET /health
```

**响应示例:**
```json
{
  "status": "healthy",
  "ocr_service": {
    "status": "healthy",
    "device": "npu:0",
    "message": "OCR服务运行正常"
  },
  "message": "服务运行正常"
}
```

### 2. OCR文本识别

```http
POST /ocr
Content-Type: multipart/form-data
```

**请求参数:**
- `file`: 图片或PDF文件

**响应示例:**
```json
{
  "filename": "test.jpg",
  "file_type": "image",
  "result": {
    "success": true,
    "total_text": "识别的文本内容",
    "text_blocks": [
      {
        "text": "识别的文本",
        "confidence": 0.95,
        "bbox": [[x1, y1], [x2, y2], [x3, y3], [x4, y4]]
      }
    ],
    "total_blocks": 1,
    "device": "npu:0"
  }
}
```

### 3. 服务信息

```http
GET /info
```

**响应示例:**
```json
{
  "service_name": "OCR RESTful Service",
  "version": "1.0.0",
  "framework": "PaddleOCR 3.1.0",
  "device": "NPU",
  "supported_formats": ["jpg", "jpeg", "png", "bmp", "pdf"],
  "endpoints": {
    "ocr": "/ocr (POST)",
    "health": "/health (GET)",
    "info": "/info (GET)"
  }
}
```

## 使用示例

### Python客户端示例

```python
import requests

# OCR识别
def ocr_recognize(file_path):
    url = "http://localhost:9527/ocr"
    
    with open(file_path, 'rb') as f:
        files = {'file': f}
        response = requests.post(url, files=files)
    
    return response.json()

# 使用示例
result = ocr_recognize("test.jpg")
print(result)
```

### cURL示例

```bash
# 识别图片
curl -X POST "http://localhost:9527/ocr" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@test.jpg"

# 识别PDF
curl -X POST "http://localhost:9527/ocr" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@test.pdf"
```

## 配置说明

### 环境变量

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| APP_HOST | 0.0.0.0 | 服务监听地址 |
| APP_PORT | 9527 | 服务端口 |
| OCR_DEVICE | npu:0 | OCR设备类型 |
| MAX_FILE_SIZE_MB | 50.0 | 最大文件大小(MB) |
| LOG_LEVEL | INFO | 日志级别 |

### 设备配置

#### NPU环境（推荐）

```bash
# 使用NPU设备
docker run -d \
  -p 9527:9527 \
  --device=/dev/davinci0:/dev/davinci0 \
  --device=/dev/davinci_manager:/dev/davinci_manager \
  --device=/dev/hisi_hdc:/dev/hisi_hdc \
  --device=/dev/devmm_svm:/dev/devmm_svm \
  --privileged \
  ocr-restful-service:1.0.0
```

#### CPU环境

```bash
# 使用CPU设备
docker run -d \
  -p 9527:9527 \
  -e OCR_DEVICE=cpu \
  ocr-restful-service:1.0.0
```

## 管理命令

### 使用运行脚本

```bash
# 启动服务
./scripts/run.sh start

# 停止服务
./scripts/run.sh stop

# 重启服务
./scripts/run.sh restart

# 查看状态
./scripts/run.sh status

# 查看日志
./scripts/run.sh logs

# 构建镜像
./scripts/run.sh build

# 清理资源
./scripts/run.sh clean
```

### 使用Docker Compose

```bash
# 启动服务
docker-compose up -d

# 停止服务
docker-compose down

# 查看日志
docker-compose logs -f

# 重启服务
docker-compose restart
```

## 性能优化

### 1. 硬件配置

- **NPU**: 推荐使用昇腾910B或更高版本
- **内存**: 建议8GB以上
- **存储**: 使用SSD存储提高I/O性能

### 2. 容器配置

```yaml
# docker-compose.yml 优化配置
services:
  ocr-service:
    # ... 其他配置
    deploy:
      resources:
        limits:
          memory: 8G
          cpus: '4.0'
        reservations:
          memory: 4G
          cpus: '2.0'
    ulimits:
      nofile:
        soft: 65536
        hard: 65536
```

### 3. 应用配置

```python
# 在config/settings.py中调整
OCR_USE_ANGLE_CLS = True  # 启用方向分类器
OCR_SHOW_LOG = False      # 关闭详细日志
MAX_FILE_SIZE_MB = 100.0  # 增加文件大小限制
```

## 故障排除

### 常见问题

1. **NPU设备不可用**
   ```
   解决方案: 服务会自动回退到CPU模式
   ```

2. **内存不足**
   ```
   解决方案: 增加容器内存限制或使用更大内存的机器
   ```

3. **文件上传失败**
   ```
   解决方案: 检查文件大小是否超过限制，或调整MAX_FILE_SIZE_MB
   ```

4. **OCR识别失败**
   ```
   解决方案: 检查图片质量，确保文字清晰可见
   ```

### 日志查看

```bash
# 查看实时日志
docker logs -f ocr-restful-service

# 查看最近100行日志
docker logs --tail 100 ocr-restful-service

# 查看错误日志
docker logs ocr-restful-service 2>&1 | grep ERROR
```

## 部署到生产环境

### 1. 镜像打包

```bash
# 构建并保存镜像
./scripts/build.sh

# 生成的tar文件可用于离线部署
# ocr-restful-service-1.0.0.tar
```

### 2. 离线部署

```bash
# 加载镜像
docker load -i ocr-restful-service-1.0.0.tar

# 运行容器
docker run -d -p 9527:9527 --name ocr-service ocr-restful-service:1.0.0
```

### 3. 反向代理配置

```nginx
# Nginx配置示例
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:9527;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 文件上传大小限制
        client_max_body_size 50M;
    }
}
```

## 开发指南

### 本地开发

```bash
# 克隆项目
git clone <repository-url>
cd ocrshiyan

# 安装依赖
pip install -r requirements.txt

# 运行开发服务器
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 9527
```

### 代码结构

- `app/main.py`: FastAPI应用主文件
- `app/ocr_service.py`: OCR服务核心逻辑
- `app/utils.py`: 工具函数
- `config/settings.py`: 配置文件

### 测试

```bash
# 运行测试
python -m pytest tests/

# 健康检查测试
curl http://localhost:9527/health
```

## 许可证

本项目基于MIT许可证开源。

## 贡献

欢迎提交Issue和Pull Request来改进项目。

## 联系方式

如有问题或建议，请通过以下方式联系：

- 提交GitHub Issue
- 发送邮件至项目维护者

---

**注意**: 本项目基于飞桨官方NPU环境构建，确保在昇腾NPU环境下获得最佳性能。 