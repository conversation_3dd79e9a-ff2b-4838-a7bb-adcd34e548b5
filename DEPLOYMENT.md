# OCR RESTful Service 部署指南

## 概述

本文档提供了完整的OCR RESTful Service部署方案，包括基于飞桨官方NPU环境的Docker容器化部署。

## 部署方案

### 方案一：基于官方NPU环境（推荐）

#### 1. 环境准备

确保您的环境满足以下要求：

- **硬件**: 昇腾NPU设备（如昇腾910B）
- **操作系统**: Ubuntu 20.04+ 或 CentOS 7+
- **Docker**: 20.10+
- **内存**: 至少8GB
- **存储**: 至少20GB可用空间

#### 2. 检查NPU环境

```bash
# 检查NPU设备
npu-smi info

# 检查CANN环境
echo $ASCEND_HOME
```

#### 3. 构建镜像

```bash
# 克隆项目
git clone <repository-url>
cd ocrshiyan

# 给脚本执行权限
chmod +x scripts/*.sh

# 构建镜像
./scripts/build.sh
```

构建过程将：
1. 基于飞桨官方NPU环境
2. 安装PaddleOCR 3.1.0
3. 配置NPU设备支持
4. 生成可部署的tar文件

#### 4. 部署服务

```bash
# 方法1: 使用运行脚本
./scripts/run.sh start

# 方法2: 使用docker-compose
docker-compose up -d

# 方法3: 直接使用Docker命令
docker run -d \
  --name ocr-restful-service \
  -p 9527:9527 \
  --device=/dev/davinci0:/dev/davinci0 \
  --device=/dev/davinci_manager:/dev/davinci_manager \
  --device=/dev/hisi_hdc:/dev/hisi_hdc \
  --device=/dev/devmm_svm:/dev/devmm_svm \
  --privileged \
  -v $(pwd)/output:/app/output \
  ocr-restful-service:1.0.0
```

#### 5. 验证部署

```bash
# 检查服务状态
curl http://localhost:9527/health

# 查看服务日志
docker logs ocr-restful-service

# 运行测试
python test_client.py
```

### 方案二：CPU环境部署

如果您的环境没有NPU设备，可以使用CPU模式：

```bash
# 构建CPU版本镜像
docker build -t ocr-restful-service:cpu .

# 运行CPU版本
docker run -d \
  --name ocr-restful-service \
  -p 9527:9527 \
  -e OCR_DEVICE=cpu \
  ocr-restful-service:cpu
```

## 生产环境部署

### 1. 高可用部署

#### 使用Docker Swarm

```bash
# 初始化Swarm
docker swarm init

# 部署服务
docker stack deploy -c docker-compose.yml ocr-stack

# 查看服务状态
docker service ls
```

#### 使用Kubernetes

```yaml
# ocr-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ocr-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: ocr-service
  template:
    metadata:
      labels:
        app: ocr-service
    spec:
      containers:
      - name: ocr-service
        image: ocr-restful-service:1.0.0
        ports:
        - containerPort: 9527
        env:
        - name: OCR_DEVICE
          value: "npu:0"
        resources:
          limits:
            memory: "8Gi"
            cpu: "4"
          requests:
            memory: "4Gi"
            cpu: "2"
---
apiVersion: v1
kind: Service
metadata:
  name: ocr-service
spec:
  selector:
    app: ocr-service
  ports:
  - port: 9527
    targetPort: 9527
  type: LoadBalancer
```

### 2. 负载均衡配置

#### Nginx配置

```nginx
# /etc/nginx/sites-available/ocr-service
upstream ocr_backend {
    server 127.0.0.1:9527;
    server 127.0.0.1:9528;
    server 127.0.0.1:9529;
}

server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://ocr_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 文件上传限制
        client_max_body_size 50M;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}
```

### 3. 监控和日志

#### 使用Prometheus监控

```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'ocr-service'
    static_configs:
      - targets: ['localhost:9527']
```

#### 使用ELK Stack

```yaml
# docker-compose.monitoring.yml
version: '3.8'
services:
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:7.17.0
    environment:
      - discovery.type=single-node
    ports:
      - "9200:9200"
  
  logstash:
    image: docker.elastic.co/logstash/logstash:7.17.0
    volumes:
      - ./logstash.conf:/usr/share/logstash/pipeline/logstash.conf
    ports:
      - "5044:5044"
  
  kibana:
    image: docker.elastic.co/kibana/kibana:7.17.0
    ports:
      - "5601:5601"
```

## 性能优化

### 1. 硬件优化

- **NPU配置**: 确保使用最新的昇腾NPU驱动
- **内存配置**: 建议16GB以上内存
- **存储配置**: 使用NVMe SSD提高I/O性能
- **网络配置**: 使用千兆或万兆网络

### 2. 容器优化

```yaml
# docker-compose.optimized.yml
version: '3.8'
services:
  ocr-service:
    build: .
    container_name: ocr-restful-service
    ports:
      - "9527:9527"
    environment:
      - OCR_DEVICE=npu:0
      - MAX_FILE_SIZE_MB=100.0
    deploy:
      resources:
        limits:
          memory: 16G
          cpus: '8.0'
        reservations:
          memory: 8G
          cpus: '4.0'
    ulimits:
      nofile:
        soft: 65536
        hard: 65536
    volumes:
      - ./output:/app/output
      - /tmp:/tmp
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9527/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
```

### 3. 应用优化

```python
# 在config/settings.py中调整
OCR_USE_ANGLE_CLS = True      # 启用方向分类器
OCR_SHOW_LOG = False          # 关闭详细日志
MAX_FILE_SIZE_MB = 100.0      # 增加文件大小限制
LOG_LEVEL = "WARNING"          # 减少日志输出
```

## 安全配置

### 1. 网络安全

```bash
# 防火墙配置
sudo ufw allow 9527/tcp
sudo ufw enable

# 或使用iptables
sudo iptables -A INPUT -p tcp --dport 9527 -j ACCEPT
```

### 2. 容器安全

```yaml
# docker-compose.secure.yml
version: '3.8'
services:
  ocr-service:
    build: .
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp
      - /var/tmp
    cap_drop:
      - ALL
    cap_add:
      - CHOWN
      - SETGID
      - SETUID
```

### 3. API安全

```python
# 添加API密钥验证
from fastapi import HTTPException, Depends
from fastapi.security import HTTPBearer

security = HTTPBearer()

async def verify_token(token: str = Depends(security)):
    if token.credentials != "your-api-key":
        raise HTTPException(status_code=401, detail="Invalid API key")
    return token.credentials

@app.post("/ocr")
async def ocr_recognize(
    file: UploadFile = File(...),
    token: str = Depends(verify_token)
):
    # OCR处理逻辑
    pass
```

## 故障排除

### 1. 常见问题

#### NPU设备不可用
```bash
# 检查NPU设备
npu-smi info

# 检查驱动
lsmod | grep davinci

# 重启NPU服务
sudo systemctl restart ascend-dmi
```

#### 内存不足
```bash
# 检查内存使用
free -h

# 增加swap空间
sudo fallocate -l 4G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
```

#### 容器启动失败
```bash
# 查看容器日志
docker logs ocr-restful-service

# 检查端口占用
netstat -tulpn | grep 9527

# 重启Docker服务
sudo systemctl restart docker
```

### 2. 性能调优

#### 调整NPU配置
```bash
# 设置NPU环境变量
export ASCEND_DEVICE_ID=0
export ASCEND_VISIBLE_DEVICES=0

# 调整NPU内存
export ASCEND_MEMORY_POOL_SIZE=8589934592
```

#### 优化Docker配置
```json
// /etc/docker/daemon.json
{
  "default-runtime": "nvidia",
  "runtimes": {
    "nvidia": {
      "path": "nvidia-container-runtime",
      "runtimeArgs": []
    }
  },
  "storage-driver": "overlay2",
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  }
}
```

## 备份和恢复

### 1. 数据备份

```bash
# 备份配置文件
tar -czf ocr-config-backup.tar.gz config/

# 备份输出目录
tar -czf ocr-output-backup.tar.gz output/

# 备份镜像
docker save ocr-restful-service:1.0.0 > ocr-image-backup.tar
```

### 2. 数据恢复

```bash
# 恢复镜像
docker load < ocr-image-backup.tar

# 恢复配置
tar -xzf ocr-config-backup.tar.gz

# 恢复输出数据
tar -xzf ocr-output-backup.tar.gz
```

## 更新和升级

### 1. 服务更新

```bash
# 停止服务
./scripts/run.sh stop

# 拉取新镜像
docker pull ocr-restful-service:latest

# 启动服务
./scripts/run.sh start
```

### 2. 版本升级

```bash
# 备份当前版本
docker tag ocr-restful-service:1.0.0 ocr-restful-service:backup

# 构建新版本
./scripts/build.sh

# 测试新版本
docker run --rm -p 9528:9527 ocr-restful-service:1.0.1

# 如果测试通过，更新生产环境
./scripts/run.sh stop
docker tag ocr-restful-service:1.0.1 ocr-restful-service:latest
./scripts/run.sh start
```

## 总结

本部署方案提供了：

1. **完整的Docker容器化部署**
2. **NPU硬件加速支持**
3. **高可用和负载均衡配置**
4. **监控和日志管理**
5. **安全配置建议**
6. **性能优化指南**
7. **故障排除方案**

通过遵循本指南，您可以成功部署一个高性能、高可用的OCR RESTful服务。 