#!/bin/bash

# OCR RESTful Service 运行脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
CONTAINER_NAME="ocr-restful-service"
IMAGE_NAME="ocr-restful-service:1.0.0"
PORT=9527

# 显示帮助信息
show_help() {
    echo -e "${BLUE}OCR RESTful Service 管理脚本${NC}"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  start     启动服务"
    echo "  stop      停止服务"
    echo "  restart   重启服务"
    echo "  status    查看服务状态"
    echo "  logs      查看服务日志"
    echo "  build     构建镜像"
    echo "  clean     清理容器和镜像"
    echo "  help      显示此帮助信息"
    echo ""
}

# 检查Docker是否运行
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        echo -e "${RED}错误: Docker未运行，请启动Docker服务${NC}"
        exit 1
    fi
}

# 启动服务
start_service() {
    echo -e "${GREEN}启动OCR RESTful Service...${NC}"
    check_docker
    
    # 检查容器是否已存在
    if docker ps -a --format "table {{.Names}}" | grep -q "^${CONTAINER_NAME}$"; then
        echo -e "${YELLOW}容器已存在，正在启动...${NC}"
        docker start ${CONTAINER_NAME}
    else
        echo -e "${YELLOW}创建并启动新容器...${NC}"
        docker run -d \
            --name ${CONTAINER_NAME} \
            -p ${PORT}:${PORT} \
            -v $(pwd)/output:/app/output \
            -v /tmp:/tmp \
            --restart unless-stopped \
            ${IMAGE_NAME}
    fi
    
    echo -e "${GREEN}服务启动完成!${NC}"
    echo -e "${YELLOW}服务地址: http://localhost:${PORT}${NC}"
    echo -e "${YELLOW}API文档: http://localhost:${PORT}/docs${NC}"
}

# 停止服务
stop_service() {
    echo -e "${YELLOW}停止OCR RESTful Service...${NC}"
    check_docker
    
    if docker ps --format "table {{.Names}}" | grep -q "^${CONTAINER_NAME}$"; then
        docker stop ${CONTAINER_NAME}
        echo -e "${GREEN}服务已停止${NC}"
    else
        echo -e "${YELLOW}服务未运行${NC}"
    fi
}

# 重启服务
restart_service() {
    echo -e "${YELLOW}重启OCR RESTful Service...${NC}"
    stop_service
    sleep 2
    start_service
}

# 查看服务状态
status_service() {
    echo -e "${BLUE}OCR RESTful Service 状态:${NC}"
    check_docker
    
    if docker ps --format "table {{.Names}}" | grep -q "^${CONTAINER_NAME}$"; then
        echo -e "${GREEN}✓ 服务正在运行${NC}"
        docker ps --filter "name=${CONTAINER_NAME}"
    else
        echo -e "${RED}✗ 服务未运行${NC}"
        if docker ps -a --format "table {{.Names}}" | grep -q "^${CONTAINER_NAME}$"; then
            echo -e "${YELLOW}容器存在但未运行${NC}"
            docker ps -a --filter "name=${CONTAINER_NAME}"
        fi
    fi
}

# 查看服务日志
logs_service() {
    echo -e "${BLUE}OCR RESTful Service 日志:${NC}"
    check_docker
    
    if docker ps --format "table {{.Names}}" | grep -q "^${CONTAINER_NAME}$"; then
        docker logs -f ${CONTAINER_NAME}
    else
        echo -e "${RED}服务未运行，无法查看日志${NC}"
    fi
}

# 构建镜像
build_service() {
    echo -e "${GREEN}构建OCR RESTful Service镜像...${NC}"
    check_docker
    
    # 运行构建脚本
    if [ -f "scripts/build.sh" ]; then
        chmod +x scripts/build.sh
        ./scripts/build.sh
    else
        echo -e "${RED}构建脚本不存在${NC}"
        exit 1
    fi
}

# 清理容器和镜像
clean_service() {
    echo -e "${YELLOW}清理OCR RESTful Service...${NC}"
    check_docker
    
    # 停止并删除容器
    if docker ps -a --format "table {{.Names}}" | grep -q "^${CONTAINER_NAME}$"; then
        docker stop ${CONTAINER_NAME} 2>/dev/null || true
        docker rm ${CONTAINER_NAME} 2>/dev/null || true
        echo -e "${GREEN}容器已清理${NC}"
    fi
    
    # 删除镜像
    if docker images --format "table {{.Repository}}:{{.Tag}}" | grep -q "^${IMAGE_NAME}$"; then
        docker rmi ${IMAGE_NAME} 2>/dev/null || true
        echo -e "${GREEN}镜像已清理${NC}"
    fi
    
    echo -e "${GREEN}清理完成${NC}"
}

# 主函数
main() {
    case "${1:-help}" in
        start)
            start_service
            ;;
        stop)
            stop_service
            ;;
        restart)
            restart_service
            ;;
        status)
            status_service
            ;;
        logs)
            logs_service
            ;;
        build)
            build_service
            ;;
        clean)
            clean_service
            ;;
        help|*)
            show_help
            ;;
    esac
}

# 执行主函数
main "$@" 