#!/bin/bash

# OCR RESTful Service ARM架构构建脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
IMAGE_NAME="ocr-restful-service"
TAG="1.0.0"
TAR_FILE="${IMAGE_NAME}-arm64-${TAG}.tar"

echo -e "${BLUE}🔨 开始构建OCR RESTful Service (ARM架构)...${NC}"

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}❌ Docker未运行，请先启动Docker${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Docker运行正常${NC}"

# 检查架构
ARCH=$(uname -m)
echo -e "${BLUE}🏗️  当前架构: ${ARCH}${NC}"

if [ "$ARCH" != "aarch64" ] && [ "$ARCH" != "arm64" ]; then
    echo -e "${YELLOW}⚠️  警告: 当前不是ARM架构，构建的镜像可能无法在ARM服务器上运行${NC}"
fi

# 构建镜像
echo -e "${BLUE}📦 构建ARM架构Docker镜像...${NC}"
docker build --platform linux/arm64 -t ${IMAGE_NAME}:${TAG} .
docker tag ${IMAGE_NAME}:${TAG} ${IMAGE_NAME}:latest

echo -e "${GREEN}✅ 镜像构建完成${NC}"

# # 保存镜像为tar文件
# echo -e "${BLUE}💾 保存镜像为tar文件...${NC}"
# docker save -o ${TAR_FILE} ${IMAGE_NAME}:${TAG}

# echo -e "${GREEN}✅ 镜像已保存为: ${TAR_FILE}${NC}"

# 显示镜像信息
echo -e "${BLUE}📊 镜像信息:${NC}"
docker images | grep ${IMAGE_NAME}

echo -e "${GREEN}🎉 构建完成!${NC}"
echo -e "${BLUE}📦 镜像文件: ${TAR_FILE}${NC}"
echo -e "${BLUE}🚀 在ARM服务器上运行命令:${NC}"
echo -e "${BLUE}   docker load -i ${TAR_FILE}${NC}"
echo -e "${BLUE}   docker run -d --privileged --network=host --shm-size=128G "
echo -e "${BLUE}     -e ASCEND_RT_VISIBLE_DEVICES=\"0,1,2,3,4,5,6,7\" "
echo -e "${BLUE}     -v \$(pwd)/output:/app/output \\${NC}"
echo -e "${BLUE}     -v \$(pwd)/temp:/app/temp \\${NC}"
echo -e "${BLUE}     -v /usr/local/Ascend/driver:/usr/local/Ascend/driver "
echo -e "${BLUE}     -v /usr/local/bin/npu-smi:/usr/local/bin/npu-smi "
echo -e "${BLUE}     -v /usr/local/dcmi:/usr/local/dcmi "
echo -e "${BLUE}     ${IMAGE_NAME}:${TAG}${NC}" 