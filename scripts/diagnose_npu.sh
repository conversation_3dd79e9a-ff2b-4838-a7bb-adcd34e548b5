#!/bin/bash

# NPU库文件诊断脚本

echo "=== NPU库文件诊断 ==="

CONTAINER_NAME="ocr-restful-service"

echo "1. 检查容器是否运行..."
if ! docker ps | grep -q ${CONTAINER_NAME}; then
    echo "容器未运行，启动临时容器进行诊断..."
    docker run --rm -it \
        --privileged \
        -v /usr/local/Ascend:/usr/local/Ascend \
        ocr-restful-service:latest \
        /bin/bash -c "
        echo '=== 系统信息 ==='
        uname -a
        
        echo -e '\n=== 检查libmsprofiler.so位置 ==='
        find /usr/local/Ascend -name 'libmsprofiler.so*' 2>/dev/null || echo '未找到libmsprofiler.so'
        find /usr/local -name 'libmsprofiler.so*' 2>/dev/null || echo '在/usr/local下未找到'
        find /usr -name 'libmsprofiler.so*' 2>/dev/null || echo '在/usr下未找到'
        
        echo -e '\n=== 检查libpaddle-custom-npu.so依赖 ==='
        if [ -f '/usr/local/lib/python3.10/dist-packages/paddle_custom_device/libpaddle-custom-npu.so' ]; then
            ldd /usr/local/lib/python3.10/dist-packages/paddle_custom_device/libpaddle-custom-npu.so | grep -E '(not found|libmsprofiler)'
        else
            echo 'libpaddle-custom-npu.so 不存在'
        fi
        
        echo -e '\n=== 当前LD_LIBRARY_PATH ==='
        echo \$LD_LIBRARY_PATH
        
        echo -e '\n=== 昇腾目录结构 ==='
        ls -la /usr/local/Ascend/ 2>/dev/null || echo '昇腾目录不存在'
        
        echo -e '\n=== 检查昇腾库目录 ==='
        find /usr/local/Ascend -name '*.so' -path '*/lib*' | head -20
        "
else
    echo "使用运行中的容器进行诊断..."
    docker exec ${CONTAINER_NAME} /bin/bash -c "
        echo '=== 检查libmsprofiler.so位置 ==='
        find /usr/local/Ascend -name 'libmsprofiler.so*' 2>/dev/null || echo '未找到libmsprofiler.so'
        
        echo -e '\n=== 检查libpaddle-custom-npu.so依赖 ==='
        ldd /usr/local/lib/python3.10/dist-packages/paddle_custom_device/libpaddle-custom-npu.so | grep -E '(not found|libmsprofiler)'
        
        echo -e '\n=== 当前LD_LIBRARY_PATH ==='
        echo \$LD_LIBRARY_PATH
        
        echo -e '\n=== 测试库加载 ==='
        python3 -c '
import os
print(\"Python LD_LIBRARY_PATH:\", os.environ.get(\"LD_LIBRARY_PATH\", \"未设置\"))
try:
    import paddle_custom_npu
    print(\"paddle_custom_npu 导入成功\")
except Exception as e:
    print(\"paddle_custom_npu 导入失败:\", e)
'
        "
fi

echo -e "\n=== 主机端检查 ==="
echo "检查主机端libmsprofiler.so位置..."
find /usr/local/Ascend -name 'libmsprofiler.so*' 2>/dev/null || echo "主机端未找到libmsprofiler.so"

echo -e "\n诊断完成！"
