#!/bin/bash

# OCR RESTful Service ARM架构CPU模式运行脚本
# 强制使用CPU模式，避免NPU初始化问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
IMAGE_NAME="ocr-restful-service"
CONTAINER_NAME="ocr-restful-service-cpu"
PORT="9527"

# 检查Docker是否运行
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        echo -e "${RED}❌ Docker未运行，请先启动Docker${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ Docker运行正常${NC}"
}

# 启动CPU模式服务
start_cpu_service() {
    echo -e "${BLUE}🚀 启动OCR服务（CPU模式）...${NC}"
    
    # 停止并删除现有容器
    docker stop ${CONTAINER_NAME} 2>/dev/null || true
    docker rm ${CONTAINER_NAME} 2>/dev/null || true
    
    # 启动新容器（CPU模式）
    docker run -d \
        --name ${CONTAINER_NAME} \
        --network=host \
        --shm-size=8G \
        -e PYTHONUNBUFFERED=1 \
        -e FORCE_CPU_MODE=1 \
        -e OCR_DEVICE=cpu \
        -v $(pwd)/output:/app/output \
        -v $(pwd)/temp:/app/temp \
        ${IMAGE_NAME}:latest
    
    echo -e "${GREEN}✅ CPU模式服务启动完成${NC}"
    echo -e "${BLUE}📊 服务地址: http://localhost:${PORT}${NC}"
}

# 停止服务
stop_service() {
    echo -e "${BLUE}🛑 停止OCR服务...${NC}"
    docker stop ${CONTAINER_NAME} 2>/dev/null || true
    docker rm ${CONTAINER_NAME} 2>/dev/null || true
    echo -e "${GREEN}✅ 服务已停止${NC}"
}

# 查看服务状态
status_service() {
    echo -e "${BLUE}📊 服务状态:${NC}"
    if docker ps | grep -q ${CONTAINER_NAME}; then
        echo -e "${GREEN}✅ 服务正在运行${NC}"
        docker ps | grep ${CONTAINER_NAME}
    else
        echo -e "${RED}❌ 服务未运行${NC}"
    fi
}

# 查看日志
logs_service() {
    echo -e "${BLUE}📋 服务日志:${NC}"
    docker logs ${CONTAINER_NAME} -f
}

# 测试服务
test_service() {
    echo -e "${BLUE}🧪 测试服务...${NC}"
    sleep 10  # 等待服务启动
    
    if curl -f http://localhost:${PORT}/health > /dev/null 2>&1; then
        echo -e "${GREEN}✅ 服务健康检查通过${NC}"
        curl -s http://localhost:${PORT}/health | python3 -m json.tool
    else
        echo -e "${RED}❌ 服务健康检查失败${NC}"
    fi
}

# 主函数
main() {
    case "${1:-help}" in
        "start")
            check_docker
            start_cpu_service
            test_service
            ;;
        "stop")
            stop_service
            ;;
        "restart")
            stop_service
            start_cpu_service
            test_service
            ;;
        "status")
            status_service
            ;;
        "logs")
            logs_service
            ;;
        "test")
            test_service
            ;;
        "help"|*)
            echo -e "${BLUE}OCR RESTful Service ARM架构CPU模式脚本${NC}"
            echo ""
            echo "用法: $0 [命令]"
            echo ""
            echo "命令:"
            echo "  start    - 启动CPU模式服务"
            echo "  stop     - 停止服务"
            echo "  restart  - 重启服务"
            echo "  status   - 查看服务状态"
            echo "  logs     - 查看服务日志"
            echo "  test     - 测试服务"
            echo "  help     - 显示帮助信息"
            echo ""
            echo "示例:"
            echo "  $0 start    # 启动CPU模式服务"
            echo "  $0 status   # 查看状态"
            ;;
    esac
}

# 执行主函数
main "$@"
