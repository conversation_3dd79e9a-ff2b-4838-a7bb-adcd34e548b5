#!/bin/bash

# OCR RESTful Service ARM架构调试运行脚本
# 适用于昇腾NPU环境 - 调试模式

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
IMAGE_NAME="ocr-restful-service"
CONTAINER_NAME="ocr-restful-service-debug"
PORT="9527"

# 检查Docker是否运行
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        echo -e "${RED}❌ Docker未运行，请先启动Docker${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ Docker运行正常${NC}"
}

# 检查NPU设备
check_npu() {
    echo -e "${BLUE}🔍 检查NPU设备...${NC}"
    if command -v npu-smi &> /dev/null; then
        echo -e "${GREEN}✅ NPU-SMI工具可用${NC}"
        npu-smi info
    else
        echo -e "${YELLOW}⚠️  NPU-SMI工具不可用，请确保已安装昇腾驱动${NC}"
    fi
}

# 启动调试服务
start_debug_service() {
    echo -e "${BLUE}🚀 启动OCR调试服务...${NC}"
    
    # 停止并删除现有容器
    docker stop ${CONTAINER_NAME} 2>/dev/null || true
    docker rm ${CONTAINER_NAME} 2>/dev/null || true
    
    # 启动新容器（调试模式）
    docker run -d \
        --name ${CONTAINER_NAME} \
        --privileged \
        --network=host \
        --shm-size=128G \
        -e ASCEND_RT_VISIBLE_DEVICES="0,1,2,3,4,5,6,7" \
        -e LD_LIBRARY_PATH="/usr/local/Ascend/lib64:/usr/local/Ascend/driver/lib64/driver:/usr/local/Ascend/driver/lib64/common:/usr/local/Ascend/driver/lib64" \
        -e PYTHONUNBUFFERED=1 \
        -e PADDLE_INIT_TIMEOUT=600 \
        -e PADDLE_DEBUG=1 \
        -e GLOG_v=3 \
        -v $(pwd)/output:/app/output \
        -v $(pwd)/temp:/app/temp \
        -v /usr/local/Ascend:/usr/local/Ascend \
        -v /usr/local/bin/npu-smi:/usr/local/bin/npu-smi \
        -v /usr/local/dcmi:/usr/local/dcmi \
        ${IMAGE_NAME}:latest
    
    echo -e "${GREEN}✅ 调试服务启动完成${NC}"
    echo -e "${BLUE}📊 服务地址: http://localhost:${PORT}${NC}"
    echo -e "${YELLOW}🔍 调试模式已启用，查看详细日志请使用: docker logs -f ${CONTAINER_NAME}${NC}"
}

# 启动交互式调试容器
start_interactive() {
    echo -e "${BLUE}🔧 启动交互式调试容器...${NC}"
    
    # 停止并删除现有容器
    docker stop ${CONTAINER_NAME}-interactive 2>/dev/null || true
    docker rm ${CONTAINER_NAME}-interactive 2>/dev/null || true
    
    # 启动交互式容器
    docker run -it \
        --name ${CONTAINER_NAME}-interactive \
        --privileged \
        --network=host \
        --shm-size=128G \
        -e ASCEND_RT_VISIBLE_DEVICES="0,1,2,3,4,5,6,7" \
        -e LD_LIBRARY_PATH="/usr/local/Ascend/lib64:/usr/local/Ascend/driver/lib64/driver:/usr/local/Ascend/driver/lib64/common:/usr/local/Ascend/driver/lib64" \
        -e PYTHONUNBUFFERED=1 \
        -v $(pwd)/output:/app/output \
        -v $(pwd)/temp:/app/temp \
        -v /usr/local/Ascend:/usr/local/Ascend \
        -v /usr/local/bin/npu-smi:/usr/local/bin/npu-smi \
        -v /usr/local/dcmi:/usr/local/dcmi \
        ${IMAGE_NAME}:latest \
        /bin/bash
}

# 测试PaddleOCR命令行
test_paddleocr() {
    echo -e "${BLUE}🧪 测试PaddleOCR命令行...${NC}"
    
    if ! docker ps | grep -q ${CONTAINER_NAME}; then
        echo -e "${RED}❌ 调试容器未运行，请先启动调试服务${NC}"
        exit 1
    fi
    
    echo -e "${YELLOW}测试CPU模式:${NC}"
    docker exec ${CONTAINER_NAME} paddleocr ocr -i /app/test_image.png --device cpu || true
    
    echo -e "${YELLOW}测试NPU模式:${NC}"
    docker exec ${CONTAINER_NAME} paddleocr ocr -i /app/test_image.png --device npu || true
}

# 查看环境信息
show_env_info() {
    echo -e "${BLUE}📋 环境信息:${NC}"
    
    if ! docker ps | grep -q ${CONTAINER_NAME}; then
        echo -e "${RED}❌ 调试容器未运行，请先启动调试服务${NC}"
        exit 1
    fi
    
    echo -e "${YELLOW}Python版本:${NC}"
    docker exec ${CONTAINER_NAME} python --version
    
    echo -e "${YELLOW}PaddlePaddle版本:${NC}"
    docker exec ${CONTAINER_NAME} python -c "import paddle; print(paddle.__version__)"
    
    echo -e "${YELLOW}PaddleOCR版本:${NC}"
    docker exec ${CONTAINER_NAME} python -c "import paddleocr; print(paddleocr.__version__)"
    
    echo -e "${YELLOW}NPU设备信息:${NC}"
    docker exec ${CONTAINER_NAME} python -c "
import paddle
print('Available devices:', paddle.device.get_all_custom_device_type())
try:
    import paddle_custom_npu
    print('NPU plugin loaded successfully')
except Exception as e:
    print('NPU plugin load failed:', e)
"
    
    echo -e "${YELLOW}环境变量:${NC}"
    docker exec ${CONTAINER_NAME} env | grep -E "(LD_LIBRARY_PATH|ASCEND|PADDLE)"
}

# 停止调试服务
stop_debug_service() {
    echo -e "${BLUE}🛑 停止调试服务...${NC}"
    docker stop ${CONTAINER_NAME} 2>/dev/null || true
    docker rm ${CONTAINER_NAME} 2>/dev/null || true
    docker stop ${CONTAINER_NAME}-interactive 2>/dev/null || true
    docker rm ${CONTAINER_NAME}-interactive 2>/dev/null || true
    echo -e "${GREEN}✅ 调试服务已停止${NC}"
}

# 查看调试日志
show_debug_logs() {
    echo -e "${BLUE}📋 调试日志:${NC}"
    if docker ps | grep -q ${CONTAINER_NAME}; then
        docker logs ${CONTAINER_NAME} -f
    else
        echo -e "${RED}❌ 调试容器未运行${NC}"
    fi
}

# 主函数
main() {
    case "${1:-help}" in
        "start")
            check_docker
            check_npu
            start_debug_service
            ;;
        "interactive")
            check_docker
            start_interactive
            ;;
        "test")
            test_paddleocr
            ;;
        "env")
            show_env_info
            ;;
        "stop")
            stop_debug_service
            ;;
        "logs")
            show_debug_logs
            ;;
        "help"|*)
            echo -e "${BLUE}OCR RESTful Service ARM架构调试脚本${NC}"
            echo ""
            echo "用法: $0 [命令]"
            echo ""
            echo "命令:"
            echo "  start       - 启动调试服务"
            echo "  interactive - 启动交互式调试容器"
            echo "  test        - 测试PaddleOCR命令行"
            echo "  env         - 查看环境信息"
            echo "  stop        - 停止调试服务"
            echo "  logs        - 查看调试日志"
            echo "  help        - 显示帮助信息"
            echo ""
            echo "示例:"
            echo "  $0 start        # 启动调试服务"
            echo "  $0 interactive  # 进入交互式调试"
            echo "  $0 test         # 测试PaddleOCR"
            echo "  $0 env          # 查看环境信息"
            ;;
    esac
}

# 执行主函数
main "$@"
