#!/bin/bash

# OCR RESTful Service ARM架构运行脚本
# 适用于昇腾NPU环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
IMAGE_NAME="ocr-restful-service"
CONTAINER_NAME="ocr-restful-service"
PORT="9527"

# 检查Docker是否运行
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        echo -e "${RED}❌ Docker未运行，请先启动Docker${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ Docker运行正常${NC}"
}

# 检查NPU设备
check_npu() {
    echo -e "${BLUE}🔍 检查NPU设备...${NC}"
    if command -v npu-smi &> /dev/null; then
        echo -e "${GREEN}✅ NPU-SMI工具可用${NC}"
        npu-smi info
    else
        echo -e "${YELLOW}⚠️  NPU-SMI工具不可用，请确保已安装昇腾驱动${NC}"
    fi
}

# 构建镜像
build_image() {
    echo -e "${BLUE}🔨 构建ARM架构镜像...${NC}"
    docker build -t ${IMAGE_NAME}:latest .
    echo -e "${GREEN}✅ 镜像构建完成${NC}"
}

# 启动服务
start_service() {
    echo -e "${BLUE}🚀 启动OCR服务...${NC}"
    
    # 停止并删除现有容器
    docker stop ${CONTAINER_NAME} 2>/dev/null || true
    docker rm ${CONTAINER_NAME} 2>/dev/null || true
    
    # 启动新容器
    docker run -d \
        --name ${CONTAINER_NAME} \
        --privileged \
        --network=host \
        --shm-size=128G \
        -e ASCEND_RT_VISIBLE_DEVICES="0,1,2,3,4,5,6,7" \
        -e LD_LIBRARY_PATH="/usr/local/Ascend/lib64:/usr/local/Ascend/driver/lib64/driver:/usr/local/Ascend/driver/lib64/common:/usr/local/Ascend/driver/lib64:/usr/local/Ascend/nnae/8.1.RC1/runtime/lib64:/usr/local/Ascend/ascend-toolkit/8.1.RC1/runtime/lib64:/usr/local/Ascend/nnrt/8.1.RC1/runtime/lib64:/usr/local/Ascend/nnae/8.1.RC1/fwkacllib/lib64:/usr/local/Ascend/ascend-toolkit/8.1.RC1/fwkacllib/lib64" \
        -e PYTHONUNBUFFERED=1 \
        -e PADDLE_INIT_TIMEOUT=600 \
        -v $(pwd)/output:/app/output \
        -v $(pwd)/temp:/app/temp \
        -v /usr/local/Ascend:/usr/local/Ascend \
        -v /usr/local/bin/npu-smi:/usr/local/bin/npu-smi \
        -v /usr/local/dcmi:/usr/local/dcmi \
        ${IMAGE_NAME}:latest
    
    echo -e "${GREEN}✅ 服务启动完成${NC}"
    echo -e "${BLUE}📊 服务地址: http://localhost:${PORT}${NC}"
}

# 停止服务
stop_service() {
    echo -e "${BLUE}🛑 停止OCR服务...${NC}"
    docker stop ${CONTAINER_NAME} 2>/dev/null || true
    docker rm ${CONTAINER_NAME} 2>/dev/null || true
    echo -e "${GREEN}✅ 服务已停止${NC}"
}

# 重启服务
restart_service() {
    echo -e "${BLUE}🔄 重启OCR服务...${NC}"
    stop_service
    start_service
}

# 查看服务状态
status_service() {
    echo -e "${BLUE}📊 服务状态:${NC}"
    if docker ps | grep -q ${CONTAINER_NAME}; then
        echo -e "${GREEN}✅ 服务正在运行${NC}"
        docker ps | grep ${CONTAINER_NAME}
    else
        echo -e "${RED}❌ 服务未运行${NC}"
    fi
}

# 查看日志
logs_service() {
    echo -e "${BLUE}📋 服务日志:${NC}"
    docker logs ${CONTAINER_NAME} -f
}

# 清理资源
cleanup() {
    echo -e "${BLUE}🧹 清理资源...${NC}"
    docker stop ${CONTAINER_NAME} 2>/dev/null || true
    docker rm ${CONTAINER_NAME} 2>/dev/null || true
    docker rmi ${IMAGE_NAME}:latest 2>/dev/null || true
    echo -e "${GREEN}✅ 清理完成${NC}"
}

# 测试服务
test_service() {
    echo -e "${BLUE}🧪 测试服务...${NC}"
    sleep 10  # 等待服务启动
    
    if curl -f http://localhost:${PORT}/health > /dev/null 2>&1; then
        echo -e "${GREEN}✅ 服务健康检查通过${NC}"
        curl -s http://localhost:${PORT}/health | python3 -m json.tool
    else
        echo -e "${RED}❌ 服务健康检查失败${NC}"
    fi
}

# 主函数
main() {
    case "${1:-help}" in
        "build")
            check_docker
            build_image
            ;;
        "start")
            check_docker
            check_npu
            start_service
            test_service
            ;;
        "stop")
            stop_service
            ;;
        "restart")
            restart_service
            ;;
        "status")
            status_service
            ;;
        "logs")
            logs_service
            ;;
        "test")
            test_service
            ;;
        "cleanup")
            cleanup
            ;;
        "help"|*)
            echo -e "${BLUE}OCR RESTful Service ARM架构管理脚本${NC}"
            echo ""
            echo "用法: $0 [命令]"
            echo ""
            echo "命令:"
            echo "  build    - 构建ARM架构镜像"
            echo "  start    - 启动服务"
            echo "  stop     - 停止服务"
            echo "  restart  - 重启服务"
            echo "  status   - 查看服务状态"
            echo "  logs     - 查看服务日志"
            echo "  test     - 测试服务"
            echo "  cleanup  - 清理资源"
            echo "  help     - 显示帮助信息"
            echo ""
            echo "示例:"
            echo "  $0 build    # 构建镜像"
            echo "  $0 start    # 启动服务"
            echo "  $0 status   # 查看状态"
            ;;
    esac
}

# 执行主函数
main "$@" 