#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OCR RESTful Service 测试客户端
"""

import requests
import json
import time
import os
from typing import Dict, Any

class OCRTestClient:
    """OCR服务测试客户端"""
    
    def __init__(self, base_url: str = "http://localhost:9527"):
        self.base_url = base_url
        self.session = requests.Session()
    
    def test_health(self) -> Dict[str, Any]:
        """测试健康检查"""
        print("🔍 测试健康检查...")
        try:
            response = self.session.get(f"{self.base_url}/health")
            result = response.json()
            print(f"✅ 健康检查成功: {result}")
            return result
        except Exception as e:
            print(f"❌ 健康检查失败: {e}")
            return {"error": str(e)}
    
    def test_info(self) -> Dict[str, Any]:
        """测试服务信息"""
        print("📋 获取服务信息...")
        try:
            response = self.session.get(f"{self.base_url}/info")
            result = response.json()
            print(f"✅ 服务信息: {result}")
            return result
        except Exception as e:
            print(f"❌ 获取服务信息失败: {e}")
            return {"error": str(e)}
    
    def test_ocr_image(self, image_path: str) -> Dict[str, Any]:
        """测试图片OCR识别"""
        print(f"🖼️  测试图片OCR识别: {image_path}")
        
        if not os.path.exists(image_path):
            print(f"❌ 图片文件不存在: {image_path}")
            return {"error": "文件不存在"}
        
        try:
            with open(image_path, 'rb') as f:
                files = {'file': (os.path.basename(image_path), f, 'image/jpeg')}
                response = self.session.post(f"{self.base_url}/ocr", files=files)
            
            result = response.json()
            print(f"✅ 图片OCR识别成功")
            print(f"📄 识别结果: {result.get('result', {}).get('total_text', '无文本')}")
            return result
        except Exception as e:
            print(f"❌ 图片OCR识别失败: {e}")
            return {"error": str(e)}
    
    def test_ocr_pdf(self, pdf_path: str) -> Dict[str, Any]:
        """测试PDF OCR识别"""
        print(f"📄 测试PDF OCR识别: {pdf_path}")
        
        if not os.path.exists(pdf_path):
            print(f"❌ PDF文件不存在: {pdf_path}")
            return {"error": "文件不存在"}
        
        try:
            with open(pdf_path, 'rb') as f:
                files = {'file': (os.path.basename(pdf_path), f, 'application/pdf')}
                response = self.session.post(f"{self.base_url}/ocr", files=files)
            
            result = response.json()
            print(f"✅ PDF OCR识别成功")
            return result
        except Exception as e:
            print(f"❌ PDF OCR识别失败: {e}")
            return {"error": str(e)}
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始OCR服务测试...")
        print("=" * 50)
        
        # 测试健康检查
        health_result = self.test_health()
        print()
        
        # 测试服务信息
        info_result = self.test_info()
        print()
        
        # 测试图片OCR（如果存在测试图片）
        test_images = ["test.jpg", "test.png", "sample.jpg"]
        for img in test_images:
            if os.path.exists(img):
                self.test_ocr_image(img)
                print()
                break
        else:
            print("⚠️  未找到测试图片文件")
            print("   请将测试图片命名为 test.jpg, test.png 或 sample.jpg")
            print()
        
        # 测试PDF OCR（如果存在测试PDF）
        test_pdfs = ["test.pdf", "sample.pdf"]
        for pdf in test_pdfs:
            if os.path.exists(pdf):
                self.test_ocr_pdf(pdf)
                print()
                break
        else:
            print("⚠️  未找到测试PDF文件")
            print("   请将测试PDF命名为 test.pdf 或 sample.pdf")
            print()
        
        print("=" * 50)
        print("🎉 测试完成!")
        
        # 总结
        if health_result.get("status") == "healthy":
            print("✅ 服务运行正常")
        else:
            print("❌ 服务异常")

def create_test_image():
    """创建测试图片"""
    try:
        from PIL import Image, ImageDraw, ImageFont
        import numpy as np
        
        # 创建一个简单的测试图片
        img = Image.new('RGB', (400, 200), color='white')
        draw = ImageDraw.Draw(img)
        
        # 添加文字
        text = "OCR测试图片\nHello World\n中文测试"
        draw.text((50, 50), text, fill='black')
        
        # 保存图片
        img.save("test.jpg")
        print("✅ 已创建测试图片: test.jpg")
        return True
    except ImportError:
        print("⚠️  Pillow未安装，无法创建测试图片")
        return False
    except Exception as e:
        print(f"❌ 创建测试图片失败: {e}")
        return False

def main():
    """主函数"""
    print("OCR RESTful Service 测试客户端")
    print("=" * 50)
    
    # 创建测试客户端
    client = OCRTestClient()
    
    # 检查服务是否运行
    try:
        response = requests.get("http://localhost:9527/", timeout=5)
        print("✅ 服务连接正常")
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务，请确保服务正在运行")
        print("   运行命令: docker run -d -p 9527:9527 ocr-restful-service:1.0.0")
        return
    except Exception as e:
        print(f"❌ 连接服务失败: {e}")
        return
    
    # 创建测试图片（如果不存在）
    if not os.path.exists("test.jpg"):
        create_test_image()
    
    # 运行测试
    client.run_all_tests()

if __name__ == "__main__":
    main() 