{"base_code": {"magic": "pir", "trainable": true, "version": 1}, "program": {"regions": [{"#": "region_0", "blocks": [{"#": "block_0", "args": [], "ops": [{"#": "p", "A": [0, 1, 1, "conv2d_46.b_0"], "DA": [], "O": {"%": 1, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_46.w_0"], "DA": [], "O": {"%": 2, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [3, 32, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "p_re_lu_1.w_0"], "DA": [], "O": {"%": 3, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_44.w_2"], "DA": [], "O": {"%": 4, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_44.w_1"], "DA": [], "O": {"%": 5, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_44.b_0"], "DA": [], "O": {"%": 6, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_44.w_0"], "DA": [], "O": {"%": 7, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_45.w_0"], "DA": [], "O": {"%": 8, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 128, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_44.b_0"], "DA": [], "O": {"%": 9, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [2], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_44.w_0"], "DA": [], "O": {"%": 10, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [2, 32, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "p_re_lu_0.w_0"], "DA": [], "O": {"%": 11, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_43.w_2"], "DA": [], "O": {"%": 12, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_43.w_1"], "DA": [], "O": {"%": 13, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_43.b_0"], "DA": [], "O": {"%": 14, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_43.w_0"], "DA": [], "O": {"%": 15, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_43.w_0"], "DA": [], "O": {"%": 16, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 128, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_42.w_2"], "DA": [], "O": {"%": 17, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_42.w_1"], "DA": [], "O": {"%": 18, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_42.b_0"], "DA": [], "O": {"%": 19, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_42.w_0"], "DA": [], "O": {"%": 20, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_42.w_0"], "DA": [], "O": {"%": 21, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128, 768, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_41.w_2"], "DA": [], "O": {"%": 22, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_41.w_1"], "DA": [], "O": {"%": 23, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_41.b_0"], "DA": [], "O": {"%": 24, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_41.w_0"], "DA": [], "O": {"%": 25, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_41.w_0"], "DA": [], "O": {"%": 26, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128, 128, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_40.w_2"], "DA": [], "O": {"%": 27, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_40.w_1"], "DA": [], "O": {"%": 28, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_40.b_0"], "DA": [], "O": {"%": 29, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_40.w_0"], "DA": [], "O": {"%": 30, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_40.w_0"], "DA": [], "O": {"%": 31, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128, 128, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_39.w_2"], "DA": [], "O": {"%": 32, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_39.w_1"], "DA": [], "O": {"%": 33, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_39.b_0"], "DA": [], "O": {"%": 34, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_39.w_0"], "DA": [], "O": {"%": 35, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_39.w_0"], "DA": [], "O": {"%": 36, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128, 128, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_38.w_2"], "DA": [], "O": {"%": 37, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_38.w_1"], "DA": [], "O": {"%": 38, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_38.b_0"], "DA": [], "O": {"%": 39, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_38.w_0"], "DA": [], "O": {"%": 40, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_38.w_0"], "DA": [], "O": {"%": 41, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128, 128, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_37.w_2"], "DA": [], "O": {"%": 42, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_37.w_1"], "DA": [], "O": {"%": 43, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_37.b_0"], "DA": [], "O": {"%": 44, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_37.w_0"], "DA": [], "O": {"%": 45, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_37.w_0"], "DA": [], "O": {"%": 46, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128, 128, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_36.w_2"], "DA": [], "O": {"%": 47, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_36.w_1"], "DA": [], "O": {"%": 48, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_36.b_0"], "DA": [], "O": {"%": 49, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_36.w_0"], "DA": [], "O": {"%": 50, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_36.w_0"], "DA": [], "O": {"%": 51, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128, 128, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_35.w_2"], "DA": [], "O": {"%": 52, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_35.w_1"], "DA": [], "O": {"%": 53, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_35.b_0"], "DA": [], "O": {"%": 54, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_35.w_0"], "DA": [], "O": {"%": 55, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_35.w_0"], "DA": [], "O": {"%": 56, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128, 128, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_34.w_2"], "DA": [], "O": {"%": 57, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_34.w_1"], "DA": [], "O": {"%": 58, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_34.b_0"], "DA": [], "O": {"%": 59, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_34.w_0"], "DA": [], "O": {"%": 60, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_34.w_0"], "DA": [], "O": {"%": 61, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128, 128, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_33.w_2"], "DA": [], "O": {"%": 62, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_33.w_1"], "DA": [], "O": {"%": 63, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_33.b_0"], "DA": [], "O": {"%": 64, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_33.w_0"], "DA": [], "O": {"%": 65, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_33.w_0"], "DA": [], "O": {"%": 66, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128, 128, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_32.w_2"], "DA": [], "O": {"%": 67, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_32.w_1"], "DA": [], "O": {"%": 68, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_32.b_0"], "DA": [], "O": {"%": 69, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_32.w_0"], "DA": [], "O": {"%": 70, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_32.w_0"], "DA": [], "O": {"%": 71, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128, 128, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_31.w_2"], "DA": [], "O": {"%": 72, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_31.w_1"], "DA": [], "O": {"%": 73, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_31.b_0"], "DA": [], "O": {"%": 74, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_31.w_0"], "DA": [], "O": {"%": 75, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_31.w_0"], "DA": [], "O": {"%": 76, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128, 128, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_30.w_2"], "DA": [], "O": {"%": 77, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_30.w_1"], "DA": [], "O": {"%": 78, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_30.b_0"], "DA": [], "O": {"%": 79, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_30.w_0"], "DA": [], "O": {"%": 80, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_30.w_0"], "DA": [], "O": {"%": 81, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128, 128, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_29.w_2"], "DA": [], "O": {"%": 82, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_29.w_1"], "DA": [], "O": {"%": 83, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_29.b_0"], "DA": [], "O": {"%": 84, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_29.w_0"], "DA": [], "O": {"%": 85, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_28.w_2"], "DA": [], "O": {"%": 86, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_28.w_1"], "DA": [], "O": {"%": 87, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_28.b_0"], "DA": [], "O": {"%": 88, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_28.w_0"], "DA": [], "O": {"%": 89, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_29.b_0"], "DA": [], "O": {"%": 90, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_29.w_0"], "DA": [], "O": {"%": 91, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128, 128, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_28.b_0"], "DA": [], "O": {"%": 92, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_28.w_0"], "DA": [], "O": {"%": 93, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128, 128, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_27.w_2"], "DA": [], "O": {"%": 94, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_27.w_1"], "DA": [], "O": {"%": 95, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_27.b_0"], "DA": [], "O": {"%": 96, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_27.w_0"], "DA": [], "O": {"%": 97, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_26.w_2"], "DA": [], "O": {"%": 98, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_26.w_1"], "DA": [], "O": {"%": 99, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_26.b_0"], "DA": [], "O": {"%": 100, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_26.w_0"], "DA": [], "O": {"%": 101, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_27.b_0"], "DA": [], "O": {"%": 102, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_27.w_0"], "DA": [], "O": {"%": 103, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128, 128, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_26.b_0"], "DA": [], "O": {"%": 104, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_26.w_0"], "DA": [], "O": {"%": 105, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128, 128, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_25.w_2"], "DA": [], "O": {"%": 106, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_25.w_1"], "DA": [], "O": {"%": 107, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_25.b_0"], "DA": [], "O": {"%": 108, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_25.w_0"], "DA": [], "O": {"%": 109, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_24.w_2"], "DA": [], "O": {"%": 110, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_24.w_1"], "DA": [], "O": {"%": 111, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_24.b_0"], "DA": [], "O": {"%": 112, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_24.w_0"], "DA": [], "O": {"%": 113, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_25.b_0"], "DA": [], "O": {"%": 114, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_25.w_0"], "DA": [], "O": {"%": 115, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128, 128, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_24.b_0"], "DA": [], "O": {"%": 116, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_24.w_0"], "DA": [], "O": {"%": 117, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128, 128, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_23.w_2"], "DA": [], "O": {"%": 118, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_23.w_1"], "DA": [], "O": {"%": 119, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_23.b_0"], "DA": [], "O": {"%": 120, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_23.w_0"], "DA": [], "O": {"%": 121, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_22.w_2"], "DA": [], "O": {"%": 122, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_22.w_1"], "DA": [], "O": {"%": 123, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_22.b_0"], "DA": [], "O": {"%": 124, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_22.w_0"], "DA": [], "O": {"%": 125, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_23.b_0"], "DA": [], "O": {"%": 126, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_23.w_0"], "DA": [], "O": {"%": 127, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128, 128, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_22.b_0"], "DA": [], "O": {"%": 128, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_22.w_0"], "DA": [], "O": {"%": 129, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128, 128, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_21.w_2"], "DA": [], "O": {"%": 130, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_21.w_1"], "DA": [], "O": {"%": 131, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_21.b_0"], "DA": [], "O": {"%": 132, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_21.w_0"], "DA": [], "O": {"%": 133, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_20.w_2"], "DA": [], "O": {"%": 134, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_20.w_1"], "DA": [], "O": {"%": 135, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_20.b_0"], "DA": [], "O": {"%": 136, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_20.w_0"], "DA": [], "O": {"%": 137, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_21.b_0"], "DA": [], "O": {"%": 138, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_21.w_0"], "DA": [], "O": {"%": 139, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128, 128, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_20.b_0"], "DA": [], "O": {"%": 140, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_20.w_0"], "DA": [], "O": {"%": 141, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128, 128, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_19.w_2"], "DA": [], "O": {"%": 142, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_19.w_1"], "DA": [], "O": {"%": 143, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_19.b_0"], "DA": [], "O": {"%": 144, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_19.w_0"], "DA": [], "O": {"%": 145, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_18.w_2"], "DA": [], "O": {"%": 146, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_18.w_1"], "DA": [], "O": {"%": 147, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_18.b_0"], "DA": [], "O": {"%": 148, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_18.w_0"], "DA": [], "O": {"%": 149, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_19.b_0"], "DA": [], "O": {"%": 150, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_19.w_0"], "DA": [], "O": {"%": 151, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128, 128, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_18.b_0"], "DA": [], "O": {"%": 152, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_18.w_0"], "DA": [], "O": {"%": 153, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128, 64, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_17.w_2"], "DA": [], "O": {"%": 154, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_17.w_1"], "DA": [], "O": {"%": 155, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_17.b_0"], "DA": [], "O": {"%": 156, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_17.w_0"], "DA": [], "O": {"%": 157, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_17.b_0"], "DA": [], "O": {"%": 158, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_17.w_0"], "DA": [], "O": {"%": 159, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128, 64, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_16.w_2"], "DA": [], "O": {"%": 160, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_16.w_1"], "DA": [], "O": {"%": 161, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_16.b_0"], "DA": [], "O": {"%": 162, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_16.w_0"], "DA": [], "O": {"%": 163, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_15.w_2"], "DA": [], "O": {"%": 164, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_15.w_1"], "DA": [], "O": {"%": 165, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_15.b_0"], "DA": [], "O": {"%": 166, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_15.w_0"], "DA": [], "O": {"%": 167, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_16.b_0"], "DA": [], "O": {"%": 168, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_16.w_0"], "DA": [], "O": {"%": 169, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64, 64, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_15.b_0"], "DA": [], "O": {"%": 170, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_15.w_0"], "DA": [], "O": {"%": 171, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64, 64, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_14.w_2"], "DA": [], "O": {"%": 172, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_14.w_1"], "DA": [], "O": {"%": 173, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_14.b_0"], "DA": [], "O": {"%": 174, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_14.w_0"], "DA": [], "O": {"%": 175, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_13.w_2"], "DA": [], "O": {"%": 176, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_13.w_1"], "DA": [], "O": {"%": 177, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_13.b_0"], "DA": [], "O": {"%": 178, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_13.w_0"], "DA": [], "O": {"%": 179, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_14.b_0"], "DA": [], "O": {"%": 180, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_14.w_0"], "DA": [], "O": {"%": 181, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64, 64, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_13.b_0"], "DA": [], "O": {"%": 182, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_13.w_0"], "DA": [], "O": {"%": 183, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64, 64, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_12.w_2"], "DA": [], "O": {"%": 184, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_12.w_1"], "DA": [], "O": {"%": 185, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_12.b_0"], "DA": [], "O": {"%": 186, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_12.w_0"], "DA": [], "O": {"%": 187, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_11.w_2"], "DA": [], "O": {"%": 188, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_11.w_1"], "DA": [], "O": {"%": 189, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_11.b_0"], "DA": [], "O": {"%": 190, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_11.w_0"], "DA": [], "O": {"%": 191, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_12.b_0"], "DA": [], "O": {"%": 192, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_12.w_0"], "DA": [], "O": {"%": 193, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64, 64, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_11.b_0"], "DA": [], "O": {"%": 194, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_11.w_0"], "DA": [], "O": {"%": 195, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64, 64, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_10.w_2"], "DA": [], "O": {"%": 196, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_10.w_1"], "DA": [], "O": {"%": 197, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_10.b_0"], "DA": [], "O": {"%": 198, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_10.w_0"], "DA": [], "O": {"%": 199, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_9.w_2"], "DA": [], "O": {"%": 200, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_9.w_1"], "DA": [], "O": {"%": 201, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_9.b_0"], "DA": [], "O": {"%": 202, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_9.w_0"], "DA": [], "O": {"%": 203, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_10.b_0"], "DA": [], "O": {"%": 204, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_10.w_0"], "DA": [], "O": {"%": 205, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64, 64, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_9.b_0"], "DA": [], "O": {"%": 206, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_9.w_0"], "DA": [], "O": {"%": 207, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64, 32, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_8.w_2"], "DA": [], "O": {"%": 208, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_8.w_1"], "DA": [], "O": {"%": 209, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_8.b_0"], "DA": [], "O": {"%": 210, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_8.w_0"], "DA": [], "O": {"%": 211, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_8.b_0"], "DA": [], "O": {"%": 212, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_8.w_0"], "DA": [], "O": {"%": 213, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64, 32, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_7.w_2"], "DA": [], "O": {"%": 214, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_7.w_1"], "DA": [], "O": {"%": 215, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_7.b_0"], "DA": [], "O": {"%": 216, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_7.w_0"], "DA": [], "O": {"%": 217, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_6.w_2"], "DA": [], "O": {"%": 218, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_6.w_1"], "DA": [], "O": {"%": 219, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_6.b_0"], "DA": [], "O": {"%": 220, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_6.w_0"], "DA": [], "O": {"%": 221, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_7.b_0"], "DA": [], "O": {"%": 222, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_7.w_0"], "DA": [], "O": {"%": 223, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 32, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_6.b_0"], "DA": [], "O": {"%": 224, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_6.w_0"], "DA": [], "O": {"%": 225, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 32, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_5.w_2"], "DA": [], "O": {"%": 226, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_5.w_1"], "DA": [], "O": {"%": 227, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_5.b_0"], "DA": [], "O": {"%": 228, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_5.w_0"], "DA": [], "O": {"%": 229, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_4.w_2"], "DA": [], "O": {"%": 230, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_4.w_1"], "DA": [], "O": {"%": 231, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_4.b_0"], "DA": [], "O": {"%": 232, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_4.w_0"], "DA": [], "O": {"%": 233, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_5.b_0"], "DA": [], "O": {"%": 234, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_5.w_0"], "DA": [], "O": {"%": 235, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 32, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_4.b_0"], "DA": [], "O": {"%": 236, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_4.w_0"], "DA": [], "O": {"%": 237, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 32, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_3.w_2"], "DA": [], "O": {"%": 238, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_3.w_1"], "DA": [], "O": {"%": 239, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_3.b_0"], "DA": [], "O": {"%": 240, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_3.w_0"], "DA": [], "O": {"%": 241, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_2.w_2"], "DA": [], "O": {"%": 242, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_2.w_1"], "DA": [], "O": {"%": 243, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_2.b_0"], "DA": [], "O": {"%": 244, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_2.w_0"], "DA": [], "O": {"%": 245, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_3.b_0"], "DA": [], "O": {"%": 246, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_3.w_0"], "DA": [], "O": {"%": 247, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 32, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_2.b_0"], "DA": [], "O": {"%": 248, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_2.w_0"], "DA": [], "O": {"%": 249, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 32, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_1.w_2"], "DA": [], "O": {"%": 250, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_1.w_1"], "DA": [], "O": {"%": 251, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_1.b_0"], "DA": [], "O": {"%": 252, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_1.w_0"], "DA": [], "O": {"%": 253, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_1.w_0"], "DA": [], "O": {"%": 254, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 32, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_0.w_2"], "DA": [], "O": {"%": 255, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_0.w_1"], "DA": [], "O": {"%": 256, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_0.b_0"], "DA": [], "O": {"%": 257, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_0.w_0"], "DA": [], "O": {"%": 258, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_0.w_0"], "DA": [], "O": {"%": 259, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 3, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "1.data", "A": [{"AT": {"#": "0.a_str", "D": "image"}, "N": "name"}, {"AT": {"#": "1.a_intarray", "D": [-1, 3, -1, -1]}, "N": "shape"}, {"AT": {"#": "1.a_dtype", "D": "float32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [0, 0, ""]}, "N": "place"}], "I": [], "O": [{"%": 260, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 3, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.shape64", "A": [], "I": [{"%": 260}], "O": [{"%": 261, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 2}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}], "I": [], "O": [{"%": 262, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 3}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}], "I": [], "O": [{"%": 263, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.slice", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}]}, "N": "axes"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}]}, "N": "infer_flags"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}]}, "N": "decrease_axis"}], "I": [{"%": 261}, {"%": 262}, {"%": 263}], "O": [{"%": 264, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 3}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}], "I": [], "O": [{"%": 265, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 4}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}], "I": [], "O": [{"%": 266, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.slice", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}]}, "N": "axes"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}]}, "N": "infer_flags"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 0}]}, "N": "decrease_axis"}], "I": [{"%": 261}, {"%": 265}, {"%": 266}], "O": [{"%": 267, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.bilinear_interp", "A": [{"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_i32", "D": -1}, "N": "out_d"}, {"AT": {"#": "0.a_i32", "D": 712}, "N": "out_h"}, {"AT": {"#": "0.a_i32", "D": 488}, "N": "out_w"}, {"AT": {"#": "0.a_array", "D": []}, "N": "scale"}, {"AT": {"#": "0.a_str", "D": "bilinear"}, "N": "interp_method"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "align_corners"}, {"AT": {"#": "0.a_i32", "D": 0}, "N": "align_mode"}], "I": [{"%": 260}, {"%": 0}, {"%": 0}, {"%": 0}], "O": [{"%": 268, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 3, 712, 488], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/Sequential/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 268}, {"%": 259}], "O": [{"%": 269, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 356, 244], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/Sequential/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 269}, {"%": 256}, {"%": 255}, {"%": 258}, {"%": 257}], "O": [{"%": 270, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 356, 244], "NCHW", [], 0]}}, {"%": 271, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 272, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 273, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 274, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 275, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/Sequential/ReLU/"}, "N": "struct_name"}], "I": [{"%": 270}], "O": [{"%": 276, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 356, 244], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/Sequential/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 276}, {"%": 254}], "O": [{"%": 277, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 178, 122], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/Sequential/BatchNorm2D_1/"}, "N": "struct_name"}], "I": [{"%": 277}, {"%": 251}, {"%": 250}, {"%": 253}, {"%": 252}], "O": [{"%": 278, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 178, 122], "NCHW", [], 0]}}, {"%": 279, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 280, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 281, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 282, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 283, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/Sequential/ReLU_1/"}, "N": "struct_name"}], "I": [{"%": 278}], "O": [{"%": 284, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 178, 122], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential/ResidualBlockWithDilation/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 284}, {"%": 249}], "O": [{"%": 285, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 178, 122], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential/ResidualBlockWithDilation/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 286, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential/ResidualBlockWithDilation/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 248}, {"%": 286}], "O": [{"%": 287, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 32, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential/ResidualBlockWithDilation/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 285}, {"%": 287}], "O": [{"%": 288, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 178, 122], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential/ResidualBlockWithDilation/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 288}, {"%": 243}, {"%": 242}, {"%": 245}, {"%": 244}], "O": [{"%": 289, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 178, 122], "NCHW", [], 0]}}, {"%": 290, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 291, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 292, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 293, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 294, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential/ResidualBlockWithDilation/ReLU/"}, "N": "struct_name"}], "I": [{"%": 289}], "O": [{"%": 295, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 178, 122], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential/ResidualBlockWithDilation/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 295}, {"%": 247}], "O": [{"%": 296, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 178, 122], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential/ResidualBlockWithDilation/Conv2D_1/"}, "N": "struct_name"}], "I": [], "O": [{"%": 297, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential/ResidualBlockWithDilation/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 246}, {"%": 297}], "O": [{"%": 298, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 32, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential/ResidualBlockWithDilation/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 296}, {"%": 298}], "O": [{"%": 299, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 178, 122], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential/ResidualBlockWithDilation/BatchNorm2D_1/"}, "N": "struct_name"}], "I": [{"%": 299}, {"%": 239}, {"%": 238}, {"%": 241}, {"%": 240}], "O": [{"%": 300, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 178, 122], "NCHW", [], 0]}}, {"%": 301, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 302, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 303, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 304, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 305, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential/ResidualBlockWithDilation/"}, "N": "struct_name"}], "I": [{"%": 300}, {"%": 284}], "O": [{"%": 306, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 178, 122], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential/ResidualBlockWithDilation/ReLU_1/"}, "N": "struct_name"}], "I": [{"%": 306}], "O": [{"%": 307, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 178, 122], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 6}, {"#": "0.a_i32", "D": 6}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 3}, {"#": "0.a_i32", "D": 3}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential/ResidualBlockWithDilation_1/Sequential/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 307}, {"%": 237}], "O": [{"%": 308, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 178, 122], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential/ResidualBlockWithDilation_1/Sequential/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 309, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential/ResidualBlockWithDilation_1/Sequential/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 236}, {"%": 309}], "O": [{"%": 310, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 32, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential/ResidualBlockWithDilation_1/Sequential/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 308}, {"%": 310}], "O": [{"%": 311, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 178, 122], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential/ResidualBlockWithDilation_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 311}, {"%": 231}, {"%": 230}, {"%": 233}, {"%": 232}], "O": [{"%": 312, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 178, 122], "NCHW", [], 0]}}, {"%": 313, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 314, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 315, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 316, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 317, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential/ResidualBlockWithDilation_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 312}], "O": [{"%": 318, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 178, 122], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 6}, {"#": "0.a_i32", "D": 6}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 3}, {"#": "0.a_i32", "D": 3}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential/ResidualBlockWithDilation_1/Sequential_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 318}, {"%": 235}], "O": [{"%": 319, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 178, 122], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential/ResidualBlockWithDilation_1/Sequential_1/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 320, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential/ResidualBlockWithDilation_1/Sequential_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 234}, {"%": 320}], "O": [{"%": 321, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 32, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential/ResidualBlockWithDilation_1/Sequential_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 319}, {"%": 321}], "O": [{"%": 322, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 178, 122], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential/ResidualBlockWithDilation_1/BatchNorm2D_1/"}, "N": "struct_name"}], "I": [{"%": 322}, {"%": 227}, {"%": 226}, {"%": 229}, {"%": 228}], "O": [{"%": 323, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 178, 122], "NCHW", [], 0]}}, {"%": 324, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 325, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 326, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 327, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 328, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential/ResidualBlockWithDilation_1/"}, "N": "struct_name"}], "I": [{"%": 323}, {"%": 307}], "O": [{"%": 329, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 178, 122], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential/ResidualBlockWithDilation_1/ReLU_1/"}, "N": "struct_name"}], "I": [{"%": 329}], "O": [{"%": 330, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 178, 122], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 6}, {"#": "0.a_i32", "D": 6}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 3}, {"#": "0.a_i32", "D": 3}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential/ResidualBlockWithDilation_2/Sequential/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 330}, {"%": 225}], "O": [{"%": 331, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 178, 122], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential/ResidualBlockWithDilation_2/Sequential/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 332, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential/ResidualBlockWithDilation_2/Sequential/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 224}, {"%": 332}], "O": [{"%": 333, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 32, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential/ResidualBlockWithDilation_2/Sequential/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 331}, {"%": 333}], "O": [{"%": 334, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 178, 122], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential/ResidualBlockWithDilation_2/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 334}, {"%": 219}, {"%": 218}, {"%": 221}, {"%": 220}], "O": [{"%": 335, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 178, 122], "NCHW", [], 0]}}, {"%": 336, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 337, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 338, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 339, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 340, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential/ResidualBlockWithDilation_2/ReLU/"}, "N": "struct_name"}], "I": [{"%": 335}], "O": [{"%": 341, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 178, 122], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 6}, {"#": "0.a_i32", "D": 6}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 3}, {"#": "0.a_i32", "D": 3}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential/ResidualBlockWithDilation_2/Sequential_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 341}, {"%": 223}], "O": [{"%": 342, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 178, 122], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential/ResidualBlockWithDilation_2/Sequential_1/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 343, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential/ResidualBlockWithDilation_2/Sequential_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 222}, {"%": 343}], "O": [{"%": 344, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 32, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential/ResidualBlockWithDilation_2/Sequential_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 342}, {"%": 344}], "O": [{"%": 345, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 178, 122], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential/ResidualBlockWithDilation_2/BatchNorm2D_1/"}, "N": "struct_name"}], "I": [{"%": 345}, {"%": 215}, {"%": 214}, {"%": 217}, {"%": 216}], "O": [{"%": 346, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 178, 122], "NCHW", [], 0]}}, {"%": 347, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 348, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 349, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 350, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 351, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential/ResidualBlockWithDilation_2/"}, "N": "struct_name"}], "I": [{"%": 346}, {"%": 330}], "O": [{"%": 352, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 178, 122], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential/ResidualBlockWithDilation_2/ReLU_1/"}, "N": "struct_name"}], "I": [{"%": 352}], "O": [{"%": 353, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 178, 122], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation/Sequential/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 353}, {"%": 213}], "O": [{"%": 354, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 89, 61], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation/Sequential/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 355, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation/Sequential/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 212}, {"%": 355}], "O": [{"%": 356, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 64, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation/Sequential/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 354}, {"%": 356}], "O": [{"%": 357, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 89, 61], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation/Sequential/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 357}, {"%": 209}, {"%": 208}, {"%": 211}, {"%": 210}], "O": [{"%": 358, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 89, 61], "NCHW", [], 0]}}, {"%": 359, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 360, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 361, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 362, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 363, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 353}, {"%": 207}], "O": [{"%": 364, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 89, 61], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 365, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 206}, {"%": 365}], "O": [{"%": 366, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 64, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 364}, {"%": 366}], "O": [{"%": 367, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 89, 61], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 367}, {"%": 201}, {"%": 200}, {"%": 203}, {"%": 202}], "O": [{"%": 368, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 89, 61], "NCHW", [], 0]}}, {"%": 369, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 370, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 371, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 372, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 373, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation/ReLU/"}, "N": "struct_name"}], "I": [{"%": 368}], "O": [{"%": 374, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 89, 61], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 374}, {"%": 205}], "O": [{"%": 375, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 89, 61], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation/Conv2D_1/"}, "N": "struct_name"}], "I": [], "O": [{"%": 376, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 204}, {"%": 376}], "O": [{"%": 377, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 64, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 375}, {"%": 377}], "O": [{"%": 378, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 89, 61], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation/BatchNorm2D_1/"}, "N": "struct_name"}], "I": [{"%": 378}, {"%": 197}, {"%": 196}, {"%": 199}, {"%": 198}], "O": [{"%": 379, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 89, 61], "NCHW", [], 0]}}, {"%": 380, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 381, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 382, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 383, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 384, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation/"}, "N": "struct_name"}], "I": [{"%": 379}, {"%": 358}], "O": [{"%": 385, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 89, 61], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation/ReLU_1/"}, "N": "struct_name"}], "I": [{"%": 385}], "O": [{"%": 386, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 89, 61], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 6}, {"#": "0.a_i32", "D": 6}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 3}, {"#": "0.a_i32", "D": 3}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation_1/Sequential/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 386}, {"%": 195}], "O": [{"%": 387, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 89, 61], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation_1/Sequential/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 388, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation_1/Sequential/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 194}, {"%": 388}], "O": [{"%": 389, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 64, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation_1/Sequential/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 387}, {"%": 389}], "O": [{"%": 390, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 89, 61], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 390}, {"%": 189}, {"%": 188}, {"%": 191}, {"%": 190}], "O": [{"%": 391, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 89, 61], "NCHW", [], 0]}}, {"%": 392, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 393, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 394, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 395, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 396, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 391}], "O": [{"%": 397, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 89, 61], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 6}, {"#": "0.a_i32", "D": 6}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 3}, {"#": "0.a_i32", "D": 3}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation_1/Sequential_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 397}, {"%": 193}], "O": [{"%": 398, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 89, 61], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation_1/Sequential_1/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 399, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation_1/Sequential_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 192}, {"%": 399}], "O": [{"%": 400, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 64, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation_1/Sequential_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 398}, {"%": 400}], "O": [{"%": 401, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 89, 61], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation_1/BatchNorm2D_1/"}, "N": "struct_name"}], "I": [{"%": 401}, {"%": 185}, {"%": 184}, {"%": 187}, {"%": 186}], "O": [{"%": 402, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 89, 61], "NCHW", [], 0]}}, {"%": 403, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 404, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 405, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 406, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 407, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation_1/"}, "N": "struct_name"}], "I": [{"%": 402}, {"%": 386}], "O": [{"%": 408, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 89, 61], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation_1/ReLU_1/"}, "N": "struct_name"}], "I": [{"%": 408}], "O": [{"%": 409, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 89, 61], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 6}, {"#": "0.a_i32", "D": 6}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 3}, {"#": "0.a_i32", "D": 3}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation_2/Sequential/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 409}, {"%": 183}], "O": [{"%": 410, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 89, 61], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation_2/Sequential/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 411, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation_2/Sequential/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 182}, {"%": 411}], "O": [{"%": 412, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 64, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation_2/Sequential/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 410}, {"%": 412}], "O": [{"%": 413, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 89, 61], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation_2/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 413}, {"%": 177}, {"%": 176}, {"%": 179}, {"%": 178}], "O": [{"%": 414, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 89, 61], "NCHW", [], 0]}}, {"%": 415, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 416, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 417, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 418, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 419, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation_2/ReLU/"}, "N": "struct_name"}], "I": [{"%": 414}], "O": [{"%": 420, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 89, 61], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 6}, {"#": "0.a_i32", "D": 6}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 3}, {"#": "0.a_i32", "D": 3}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation_2/Sequential_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 420}, {"%": 181}], "O": [{"%": 421, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 89, 61], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation_2/Sequential_1/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 422, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation_2/Sequential_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 180}, {"%": 422}], "O": [{"%": 423, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 64, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation_2/Sequential_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 421}, {"%": 423}], "O": [{"%": 424, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 89, 61], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation_2/BatchNorm2D_1/"}, "N": "struct_name"}], "I": [{"%": 424}, {"%": 173}, {"%": 172}, {"%": 175}, {"%": 174}], "O": [{"%": 425, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 89, 61], "NCHW", [], 0]}}, {"%": 426, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 427, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 428, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 429, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 430, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation_2/"}, "N": "struct_name"}], "I": [{"%": 425}, {"%": 409}], "O": [{"%": 431, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 89, 61], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation_2/ReLU_1/"}, "N": "struct_name"}], "I": [{"%": 431}], "O": [{"%": 432, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 89, 61], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 6}, {"#": "0.a_i32", "D": 6}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 3}, {"#": "0.a_i32", "D": 3}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation_3/Sequential/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 432}, {"%": 171}], "O": [{"%": 433, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 89, 61], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation_3/Sequential/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 434, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation_3/Sequential/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 170}, {"%": 434}], "O": [{"%": 435, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 64, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation_3/Sequential/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 433}, {"%": 435}], "O": [{"%": 436, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 89, 61], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation_3/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 436}, {"%": 165}, {"%": 164}, {"%": 167}, {"%": 166}], "O": [{"%": 437, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 89, 61], "NCHW", [], 0]}}, {"%": 438, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 439, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 440, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 441, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 442, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation_3/ReLU/"}, "N": "struct_name"}], "I": [{"%": 437}], "O": [{"%": 443, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 89, 61], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 6}, {"#": "0.a_i32", "D": 6}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 3}, {"#": "0.a_i32", "D": 3}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation_3/Sequential_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 443}, {"%": 169}], "O": [{"%": 444, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 89, 61], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation_3/Sequential_1/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 445, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation_3/Sequential_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 168}, {"%": 445}], "O": [{"%": 446, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 64, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation_3/Sequential_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 444}, {"%": 446}], "O": [{"%": 447, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 89, 61], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation_3/BatchNorm2D_1/"}, "N": "struct_name"}], "I": [{"%": 447}, {"%": 161}, {"%": 160}, {"%": 163}, {"%": 162}], "O": [{"%": 448, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 89, 61], "NCHW", [], 0]}}, {"%": 449, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 450, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 451, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 452, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 453, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation_3/"}, "N": "struct_name"}], "I": [{"%": 448}, {"%": 432}], "O": [{"%": 454, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 89, 61], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_1/ResidualBlockWithDilation_3/ReLU_1/"}, "N": "struct_name"}], "I": [{"%": 454}], "O": [{"%": 455, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, 89, 61], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation/Sequential/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 455}, {"%": 159}], "O": [{"%": 456, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation/Sequential/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 457, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation/Sequential/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 158}, {"%": 457}], "O": [{"%": 458, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 128, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation/Sequential/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 456}, {"%": 458}], "O": [{"%": 459, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation/Sequential/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 459}, {"%": 155}, {"%": 154}, {"%": 157}, {"%": 156}], "O": [{"%": 460, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}, {"%": 461, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 462, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 463, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 464, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 465, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 455}, {"%": 153}], "O": [{"%": 466, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 467, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 152}, {"%": 467}], "O": [{"%": 468, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 128, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 466}, {"%": 468}], "O": [{"%": 469, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 469}, {"%": 147}, {"%": 146}, {"%": 149}, {"%": 148}], "O": [{"%": 470, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}, {"%": 471, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 472, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 473, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 474, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 475, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation/ReLU/"}, "N": "struct_name"}], "I": [{"%": 470}], "O": [{"%": 476, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 476}, {"%": 151}], "O": [{"%": 477, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation/Conv2D_1/"}, "N": "struct_name"}], "I": [], "O": [{"%": 478, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 150}, {"%": 478}], "O": [{"%": 479, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 128, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 477}, {"%": 479}], "O": [{"%": 480, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation/BatchNorm2D_1/"}, "N": "struct_name"}], "I": [{"%": 480}, {"%": 143}, {"%": 142}, {"%": 145}, {"%": 144}], "O": [{"%": 481, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}, {"%": 482, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 483, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 484, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 485, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 486, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation/"}, "N": "struct_name"}], "I": [{"%": 481}, {"%": 460}], "O": [{"%": 487, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation/ReLU_1/"}, "N": "struct_name"}], "I": [{"%": 487}], "O": [{"%": 488, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 6}, {"#": "0.a_i32", "D": 6}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 3}, {"#": "0.a_i32", "D": 3}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_1/Sequential/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 488}, {"%": 141}], "O": [{"%": 489, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_1/Sequential/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 490, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_1/Sequential/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 140}, {"%": 490}], "O": [{"%": 491, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 128, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_1/Sequential/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 489}, {"%": 491}], "O": [{"%": 492, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 492}, {"%": 135}, {"%": 134}, {"%": 137}, {"%": 136}], "O": [{"%": 493, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}, {"%": 494, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 495, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 496, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 497, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 498, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 493}], "O": [{"%": 499, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 6}, {"#": "0.a_i32", "D": 6}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 3}, {"#": "0.a_i32", "D": 3}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_1/Sequential_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 499}, {"%": 139}], "O": [{"%": 500, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_1/Sequential_1/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 501, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_1/Sequential_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 138}, {"%": 501}], "O": [{"%": 502, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 128, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_1/Sequential_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 500}, {"%": 502}], "O": [{"%": 503, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_1/BatchNorm2D_1/"}, "N": "struct_name"}], "I": [{"%": 503}, {"%": 131}, {"%": 130}, {"%": 133}, {"%": 132}], "O": [{"%": 504, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}, {"%": 505, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 506, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 507, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 508, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 509, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_1/"}, "N": "struct_name"}], "I": [{"%": 504}, {"%": 488}], "O": [{"%": 510, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_1/ReLU_1/"}, "N": "struct_name"}], "I": [{"%": 510}], "O": [{"%": 511, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 6}, {"#": "0.a_i32", "D": 6}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 3}, {"#": "0.a_i32", "D": 3}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_2/Sequential/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 511}, {"%": 129}], "O": [{"%": 512, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_2/Sequential/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 513, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_2/Sequential/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 128}, {"%": 513}], "O": [{"%": 514, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 128, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_2/Sequential/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 512}, {"%": 514}], "O": [{"%": 515, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_2/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 515}, {"%": 123}, {"%": 122}, {"%": 125}, {"%": 124}], "O": [{"%": 516, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}, {"%": 517, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 518, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 519, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 520, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 521, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_2/ReLU/"}, "N": "struct_name"}], "I": [{"%": 516}], "O": [{"%": 522, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 6}, {"#": "0.a_i32", "D": 6}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 3}, {"#": "0.a_i32", "D": 3}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_2/Sequential_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 522}, {"%": 127}], "O": [{"%": 523, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_2/Sequential_1/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 524, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_2/Sequential_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 126}, {"%": 524}], "O": [{"%": 525, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 128, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_2/Sequential_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 523}, {"%": 525}], "O": [{"%": 526, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_2/BatchNorm2D_1/"}, "N": "struct_name"}], "I": [{"%": 526}, {"%": 119}, {"%": 118}, {"%": 121}, {"%": 120}], "O": [{"%": 527, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}, {"%": 528, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 529, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 530, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 531, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 532, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_2/"}, "N": "struct_name"}], "I": [{"%": 527}, {"%": 511}], "O": [{"%": 533, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_2/ReLU_1/"}, "N": "struct_name"}], "I": [{"%": 533}], "O": [{"%": 534, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 6}, {"#": "0.a_i32", "D": 6}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 3}, {"#": "0.a_i32", "D": 3}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_3/Sequential/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 534}, {"%": 117}], "O": [{"%": 535, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_3/Sequential/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 536, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_3/Sequential/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 116}, {"%": 536}], "O": [{"%": 537, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 128, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_3/Sequential/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 535}, {"%": 537}], "O": [{"%": 538, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_3/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 538}, {"%": 111}, {"%": 110}, {"%": 113}, {"%": 112}], "O": [{"%": 539, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}, {"%": 540, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 541, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 542, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 543, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 544, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_3/ReLU/"}, "N": "struct_name"}], "I": [{"%": 539}], "O": [{"%": 545, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 6}, {"#": "0.a_i32", "D": 6}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 3}, {"#": "0.a_i32", "D": 3}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_3/Sequential_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 545}, {"%": 115}], "O": [{"%": 546, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_3/Sequential_1/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 547, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_3/Sequential_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 114}, {"%": 547}], "O": [{"%": 548, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 128, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_3/Sequential_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 546}, {"%": 548}], "O": [{"%": 549, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_3/BatchNorm2D_1/"}, "N": "struct_name"}], "I": [{"%": 549}, {"%": 107}, {"%": 106}, {"%": 109}, {"%": 108}], "O": [{"%": 550, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}, {"%": 551, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 552, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 553, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 554, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 555, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_3/"}, "N": "struct_name"}], "I": [{"%": 550}, {"%": 534}], "O": [{"%": 556, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_3/ReLU_1/"}, "N": "struct_name"}], "I": [{"%": 556}], "O": [{"%": 557, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 6}, {"#": "0.a_i32", "D": 6}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 3}, {"#": "0.a_i32", "D": 3}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_4/Sequential/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 557}, {"%": 105}], "O": [{"%": 558, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_4/Sequential/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 559, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_4/Sequential/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 104}, {"%": 559}], "O": [{"%": 560, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 128, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_4/Sequential/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 558}, {"%": 560}], "O": [{"%": 561, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_4/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 561}, {"%": 99}, {"%": 98}, {"%": 101}, {"%": 100}], "O": [{"%": 562, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}, {"%": 563, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 564, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 565, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 566, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 567, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_4/ReLU/"}, "N": "struct_name"}], "I": [{"%": 562}], "O": [{"%": 568, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 6}, {"#": "0.a_i32", "D": 6}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 3}, {"#": "0.a_i32", "D": 3}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_4/Sequential_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 568}, {"%": 103}], "O": [{"%": 569, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_4/Sequential_1/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 570, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_4/Sequential_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 102}, {"%": 570}], "O": [{"%": 571, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 128, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_4/Sequential_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 569}, {"%": 571}], "O": [{"%": 572, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_4/BatchNorm2D_1/"}, "N": "struct_name"}], "I": [{"%": 572}, {"%": 95}, {"%": 94}, {"%": 97}, {"%": 96}], "O": [{"%": 573, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}, {"%": 574, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 575, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 576, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 577, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 578, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_4/"}, "N": "struct_name"}], "I": [{"%": 573}, {"%": 557}], "O": [{"%": 579, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_4/ReLU_1/"}, "N": "struct_name"}], "I": [{"%": 579}], "O": [{"%": 580, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 6}, {"#": "0.a_i32", "D": 6}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 3}, {"#": "0.a_i32", "D": 3}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_5/Sequential/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 580}, {"%": 93}], "O": [{"%": 581, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_5/Sequential/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 582, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_5/Sequential/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 92}, {"%": 582}], "O": [{"%": 583, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 128, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_5/Sequential/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 581}, {"%": 583}], "O": [{"%": 584, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_5/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 584}, {"%": 87}, {"%": 86}, {"%": 89}, {"%": 88}], "O": [{"%": 585, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}, {"%": 586, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 587, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 588, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 589, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 590, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_5/ReLU/"}, "N": "struct_name"}], "I": [{"%": 585}], "O": [{"%": 591, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 6}, {"#": "0.a_i32", "D": 6}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 3}, {"#": "0.a_i32", "D": 3}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_5/Sequential_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 591}, {"%": 91}], "O": [{"%": 592, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_5/Sequential_1/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 593, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_5/Sequential_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 90}, {"%": 593}], "O": [{"%": 594, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 128, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_5/Sequential_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 592}, {"%": 594}], "O": [{"%": 595, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_5/BatchNorm2D_1/"}, "N": "struct_name"}], "I": [{"%": 595}, {"%": 83}, {"%": 82}, {"%": 85}, {"%": 84}], "O": [{"%": 596, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}, {"%": 597, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 598, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 599, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 600, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 601, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_5/"}, "N": "struct_name"}], "I": [{"%": 596}, {"%": 580}], "O": [{"%": 602, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/ResnetStraight/Sequential_2/ResidualBlockWithDilation_5/ReLU_1/"}, "N": "struct_name"}], "I": [{"%": 602}], "O": [{"%": 603, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/Sequential_1/Sequential/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 603}, {"%": 81}], "O": [{"%": 604, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/Sequential_1/Sequential/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 604}, {"%": 78}, {"%": 77}, {"%": 80}, {"%": 79}], "O": [{"%": 605, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}, {"%": 606, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 607, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 608, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 609, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 610, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/Sequential_1/Sequential/ReLU/"}, "N": "struct_name"}], "I": [{"%": 605}], "O": [{"%": 611, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/Sequential_2/Sequential/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 603}, {"%": 76}], "O": [{"%": 612, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/Sequential_2/Sequential/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 612}, {"%": 73}, {"%": 72}, {"%": 75}, {"%": 74}], "O": [{"%": 613, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}, {"%": 614, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 615, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 616, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 617, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 618, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/Sequential_2/Sequential/ReLU/"}, "N": "struct_name"}], "I": [{"%": 613}], "O": [{"%": 619, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 5}, {"#": "0.a_i32", "D": 5}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 5}, {"#": "0.a_i32", "D": 5}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/Sequential_3/Sequential/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 603}, {"%": 71}], "O": [{"%": 620, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/Sequential_3/Sequential/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 620}, {"%": 68}, {"%": 67}, {"%": 70}, {"%": 69}], "O": [{"%": 621, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}, {"%": 622, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 623, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 624, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 625, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 626, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/Sequential_3/Sequential/ReLU/"}, "N": "struct_name"}], "I": [{"%": 621}], "O": [{"%": 627, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 8}, {"#": "0.a_i32", "D": 8}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 8}, {"#": "0.a_i32", "D": 8}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/Sequential_4/Sequential/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 603}, {"%": 66}], "O": [{"%": 628, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/Sequential_4/Sequential/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 628}, {"%": 63}, {"%": 62}, {"%": 65}, {"%": 64}], "O": [{"%": 629, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}, {"%": 630, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 631, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 632, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 633, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 634, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/Sequential_4/Sequential/ReLU/"}, "N": "struct_name"}], "I": [{"%": 629}], "O": [{"%": 635, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 3}, {"#": "0.a_i32", "D": 3}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 3}, {"#": "0.a_i32", "D": 3}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/Sequential_4/Sequential_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 635}, {"%": 61}], "O": [{"%": 636, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/Sequential_4/Sequential_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 636}, {"%": 58}, {"%": 57}, {"%": 60}, {"%": 59}], "O": [{"%": 637, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}, {"%": 638, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 639, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 640, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 641, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 642, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/Sequential_4/Sequential_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 637}], "O": [{"%": 643, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/Sequential_4/Sequential_2/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 643}, {"%": 56}], "O": [{"%": 644, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/Sequential_4/Sequential_2/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 644}, {"%": 53}, {"%": 52}, {"%": 55}, {"%": 54}], "O": [{"%": 645, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}, {"%": 646, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 647, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 648, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 649, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 650, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/Sequential_4/Sequential_2/ReLU/"}, "N": "struct_name"}], "I": [{"%": 645}], "O": [{"%": 651, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 12}, {"#": "0.a_i32", "D": 12}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 12}, {"#": "0.a_i32", "D": 12}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/Sequential_5/Sequential/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 603}, {"%": 51}], "O": [{"%": 652, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/Sequential_5/Sequential/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 652}, {"%": 48}, {"%": 47}, {"%": 50}, {"%": 49}], "O": [{"%": 653, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}, {"%": 654, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 655, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 656, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 657, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 658, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/Sequential_5/Sequential/ReLU/"}, "N": "struct_name"}], "I": [{"%": 653}], "O": [{"%": 659, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 7}, {"#": "0.a_i32", "D": 7}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 7}, {"#": "0.a_i32", "D": 7}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/Sequential_5/Sequential_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 659}, {"%": 46}], "O": [{"%": 660, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/Sequential_5/Sequential_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 660}, {"%": 43}, {"%": 42}, {"%": 45}, {"%": 44}], "O": [{"%": 661, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}, {"%": 662, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 663, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 664, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 665, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 666, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/Sequential_5/Sequential_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 661}], "O": [{"%": 667, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 4}, {"#": "0.a_i32", "D": 4}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 4}, {"#": "0.a_i32", "D": 4}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/Sequential_5/Sequential_2/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 667}, {"%": 41}], "O": [{"%": 668, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/Sequential_5/Sequential_2/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 668}, {"%": 38}, {"%": 37}, {"%": 40}, {"%": 39}], "O": [{"%": 669, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}, {"%": 670, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 671, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 672, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 673, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 674, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/Sequential_5/Sequential_2/ReLU/"}, "N": "struct_name"}], "I": [{"%": 669}], "O": [{"%": 675, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 18}, {"#": "0.a_i32", "D": 18}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 18}, {"#": "0.a_i32", "D": 18}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/Sequential_6/Sequential/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 603}, {"%": 36}], "O": [{"%": 676, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/Sequential_6/Sequential/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 676}, {"%": 33}, {"%": 32}, {"%": 35}, {"%": 34}], "O": [{"%": 677, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}, {"%": 678, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 679, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 680, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 681, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 682, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/Sequential_6/Sequential/ReLU/"}, "N": "struct_name"}], "I": [{"%": 677}], "O": [{"%": 683, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 12}, {"#": "0.a_i32", "D": 12}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 12}, {"#": "0.a_i32", "D": 12}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/Sequential_6/Sequential_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 683}, {"%": 31}], "O": [{"%": 684, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/Sequential_6/Sequential_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 684}, {"%": 28}, {"%": 27}, {"%": 30}, {"%": 29}], "O": [{"%": 685, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}, {"%": 686, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 687, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 688, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 689, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 690, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/Sequential_6/Sequential_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 685}], "O": [{"%": 691, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 6}, {"#": "0.a_i32", "D": 6}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 6}, {"#": "0.a_i32", "D": 6}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/Sequential_6/Sequential_2/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 691}, {"%": 26}], "O": [{"%": 692, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/Sequential_6/Sequential_2/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 692}, {"%": 23}, {"%": 22}, {"%": 25}, {"%": 24}], "O": [{"%": 693, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}, {"%": 694, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 695, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 696, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 697, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 698, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/Sequential_6/Sequential_2/ReLU/"}, "N": "struct_name"}], "I": [{"%": 693}], "O": [{"%": 699, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 1.0}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}], "I": [], "O": [{"%": 700, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "0.combine", "A": [], "I": [{"%": 611}, {"%": 619}, {"%": 627}, {"%": 651}, {"%": 675}, {"%": 699}], "O": [{"%": 701, "TT": {"#": "0.t_vec", "D": [{"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.concat", "A": [], "I": [{"%": 701}, {"%": 700}], "O": [{"%": 702, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 768, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/Sequential_7/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 702}, {"%": 21}], "O": [{"%": 703, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/Sequential_7/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 703}, {"%": 18}, {"%": 17}, {"%": 20}, {"%": 19}], "O": [{"%": 704, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}, {"%": 705, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 706, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 707, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 708, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 709, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/Sequential_7/ReLU/"}, "N": "struct_name"}], "I": [{"%": 704}], "O": [{"%": 710, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 2}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/Sequential_8/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 711, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.unsqueeze", "A": [{"AT": {"#": "0.a_str", "D": "/Sequential_8/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 710}, {"%": 711}], "O": [{"%": 712, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 1, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 2}, {"#": "0.a_i64", "D": 2}, {"#": "0.a_i64", "D": 2}, {"#": "0.a_i64", "D": 2}, {"#": "0.a_i64", "D": 0}, {"#": "0.a_i64", "D": 0}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/Sequential_8/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 713, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [6], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.pad3d", "A": [{"AT": {"#": "0.a_str", "D": "reflect"}, "N": "mode"}, {"AT": {"#": "0.a_f32", "D": 0.0}, "N": "pad_value"}, {"AT": {"#": "0.a_str", "D": "NCDHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/Sequential_8/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 712}, {"%": 713}], "O": [{"%": 714, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 1, 49, 35], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 2}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/Sequential_8/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 715, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.squeeze", "A": [{"AT": {"#": "0.a_str", "D": "/Sequential_8/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 714}, {"%": 715}], "O": [{"%": 716, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, 49, 35], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/Sequential_8/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 716}, {"%": 16}], "O": [{"%": 717, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/Sequential_8/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 717}, {"%": 13}, {"%": 12}, {"%": 15}, {"%": 14}], "O": [{"%": 718, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 45, 31], "NCHW", [], 0]}}, {"%": 719, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 720, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 721, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 722, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 723, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.prelu", "A": [{"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "all"}, "N": "mode"}, {"AT": {"#": "0.a_str", "D": "/Sequential_8/PReLU/"}, "N": "struct_name"}], "I": [{"%": 718}, {"%": 11}], "O": [{"%": 724, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 2}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/Sequential_8/Conv2D_1/"}, "N": "struct_name"}], "I": [], "O": [{"%": 725, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.unsqueeze", "A": [{"AT": {"#": "0.a_str", "D": "/Sequential_8/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 724}, {"%": 725}], "O": [{"%": 726, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 1, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 2}, {"#": "0.a_i64", "D": 2}, {"#": "0.a_i64", "D": 2}, {"#": "0.a_i64", "D": 2}, {"#": "0.a_i64", "D": 0}, {"#": "0.a_i64", "D": 0}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/Sequential_8/Conv2D_1/"}, "N": "struct_name"}], "I": [], "O": [{"%": 727, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [6], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.pad3d", "A": [{"AT": {"#": "0.a_str", "D": "reflect"}, "N": "mode"}, {"AT": {"#": "0.a_f32", "D": 0.0}, "N": "pad_value"}, {"AT": {"#": "0.a_str", "D": "NCDHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/Sequential_8/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 726}, {"%": 727}], "O": [{"%": 728, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 1, 49, 35], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 2}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/Sequential_8/Conv2D_1/"}, "N": "struct_name"}], "I": [], "O": [{"%": 729, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.squeeze", "A": [{"AT": {"#": "0.a_str", "D": "/Sequential_8/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 728}, {"%": 729}], "O": [{"%": 730, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, 49, 35], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/Sequential_8/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 730}, {"%": 10}], "O": [{"%": 731, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 2, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/Sequential_8/Conv2D_1/"}, "N": "struct_name"}], "I": [], "O": [{"%": 732, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/Sequential_8/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 9}, {"%": 732}], "O": [{"%": 733, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 2, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/Sequential_8/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 731}, {"%": 733}], "O": [{"%": 734, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 2, 45, 31], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "0.combine", "A": [], "I": [{"%": 264}, {"%": 267}], "O": [{"%": 735, "TT": {"#": "0.t_vec", "D": [{"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [], "NCHW", [], 0]}]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.bilinear_interp", "A": [{"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_i32", "D": -1}, "N": "out_d"}, {"AT": {"#": "0.a_i32", "D": -1}, "N": "out_h"}, {"AT": {"#": "0.a_i32", "D": -1}, "N": "out_w"}, {"AT": {"#": "0.a_array", "D": []}, "N": "scale"}, {"AT": {"#": "0.a_str", "D": "bilinear"}, "N": "interp_method"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "align_corners"}, {"AT": {"#": "0.a_i32", "D": 0}, "N": "align_mode"}], "I": [{"%": 734}, {"%": 0}, {"%": 735}, {"%": 0}], "O": [{"%": 736, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 2, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.transpose", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 3}, {"#": "0.a_i32", "D": 1}]}, "N": "perm"}], "I": [{"%": 736}], "O": [{"%": 737, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, -1, -1, 2], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.grid_sample", "A": [{"AT": {"#": "0.a_str", "D": "bilinear"}, "N": "mode"}, {"AT": {"#": "0.a_str", "D": "zeros"}, "N": "padding_mode"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "align_corners"}], "I": [{"%": 260}, {"%": 737}], "O": [{"%": 738, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 3, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.fetch", "A": [{"AT": {"#": "0.a_str", "D": "fetch_name_0"}, "N": "name"}, {"AT": {"#": "0.a_i32", "D": 0}, "N": "col"}], "I": [{"%": 738}], "O": [{"%": 739, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 3, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "persistable"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}]}]}]}}