{"base_code": {"magic": "pir", "trainable": true, "version": 1}, "program": {"regions": [{"#": "region_0", "blocks": [{"#": "block_0", "args": [], "ops": [{"#": "p", "A": [0, 1, 1, "conv2d_143.b_0"], "DA": [], "O": {"%": 1, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_143.w_0"], "DA": [], "O": {"%": 2, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 64, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm_4.w_2"], "DA": [], "O": {"%": 3, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm_4.w_1"], "DA": [], "O": {"%": 4, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm_4.b_0"], "DA": [], "O": {"%": 5, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm_4.w_0"], "DA": [], "O": {"%": 6, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_142.w_0"], "DA": [], "O": {"%": 7, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64, 65, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_transpose_1.b_0"], "DA": [], "O": {"%": 8, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_transpose_1.w_0"], "DA": [], "O": {"%": 9, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64, 1, 2, 2], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm_1.w_2"], "DA": [], "O": {"%": 10, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm_1.w_1"], "DA": [], "O": {"%": 11, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm_1.b_0"], "DA": [], "O": {"%": 12, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm_1.w_0"], "DA": [], "O": {"%": 13, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_transpose_0.b_0"], "DA": [], "O": {"%": 14, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_transpose_0.w_0"], "DA": [], "O": {"%": 15, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64, 64, 2, 2], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm_0.w_2"], "DA": [], "O": {"%": 16, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm_0.w_1"], "DA": [], "O": {"%": 17, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm_0.b_0"], "DA": [], "O": {"%": 18, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm_0.w_0"], "DA": [], "O": {"%": 19, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_140.w_0"], "DA": [], "O": {"%": 20, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64, 256, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_83.w_2"], "DA": [], "O": {"%": 21, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_83.w_1"], "DA": [], "O": {"%": 22, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_83.b_0"], "DA": [], "O": {"%": 23, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_83.w_0"], "DA": [], "O": {"%": 24, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_139.b_0"], "DA": [], "O": {"%": 25, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_139.w_0"], "DA": [], "O": {"%": 26, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 32, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_138.b_0"], "DA": [], "O": {"%": 27, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_138.w_0"], "DA": [], "O": {"%": 28, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 32, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_137.b_0"], "DA": [], "O": {"%": 29, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_137.w_0"], "DA": [], "O": {"%": 30, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 32, 7, 7], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_136.b_0"], "DA": [], "O": {"%": 31, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_136.w_0"], "DA": [], "O": {"%": 32, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 32, 1, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_135.b_0"], "DA": [], "O": {"%": 33, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_135.w_0"], "DA": [], "O": {"%": 34, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 32, 1, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_134.b_0"], "DA": [], "O": {"%": 35, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_134.w_0"], "DA": [], "O": {"%": 36, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 32, 1, 7], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_133.b_0"], "DA": [], "O": {"%": 37, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_133.w_0"], "DA": [], "O": {"%": 38, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 32, 3, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_132.b_0"], "DA": [], "O": {"%": 39, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_132.w_0"], "DA": [], "O": {"%": 40, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 32, 5, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_131.b_0"], "DA": [], "O": {"%": 41, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_131.w_0"], "DA": [], "O": {"%": 42, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 32, 7, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_130.b_0"], "DA": [], "O": {"%": 43, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_130.w_0"], "DA": [], "O": {"%": 44, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64, 32, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_129.b_0"], "DA": [], "O": {"%": 45, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_129.w_0"], "DA": [], "O": {"%": 46, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 64, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_82.w_2"], "DA": [], "O": {"%": 47, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_82.w_1"], "DA": [], "O": {"%": 48, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_82.b_0"], "DA": [], "O": {"%": 49, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_82.w_0"], "DA": [], "O": {"%": 50, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_128.b_0"], "DA": [], "O": {"%": 51, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_128.w_0"], "DA": [], "O": {"%": 52, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 32, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_127.b_0"], "DA": [], "O": {"%": 53, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_127.w_0"], "DA": [], "O": {"%": 54, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 32, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_126.b_0"], "DA": [], "O": {"%": 55, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_126.w_0"], "DA": [], "O": {"%": 56, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 32, 7, 7], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_125.b_0"], "DA": [], "O": {"%": 57, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_125.w_0"], "DA": [], "O": {"%": 58, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 32, 1, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_124.b_0"], "DA": [], "O": {"%": 59, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_124.w_0"], "DA": [], "O": {"%": 60, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 32, 1, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_123.b_0"], "DA": [], "O": {"%": 61, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_123.w_0"], "DA": [], "O": {"%": 62, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 32, 1, 7], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_122.b_0"], "DA": [], "O": {"%": 63, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_122.w_0"], "DA": [], "O": {"%": 64, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 32, 3, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_121.b_0"], "DA": [], "O": {"%": 65, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_121.w_0"], "DA": [], "O": {"%": 66, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 32, 5, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_120.b_0"], "DA": [], "O": {"%": 67, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_120.w_0"], "DA": [], "O": {"%": 68, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 32, 7, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_119.b_0"], "DA": [], "O": {"%": 69, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_119.w_0"], "DA": [], "O": {"%": 70, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64, 32, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_118.b_0"], "DA": [], "O": {"%": 71, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_118.w_0"], "DA": [], "O": {"%": 72, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 64, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_81.w_2"], "DA": [], "O": {"%": 73, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_81.w_1"], "DA": [], "O": {"%": 74, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_81.b_0"], "DA": [], "O": {"%": 75, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_81.w_0"], "DA": [], "O": {"%": 76, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_117.b_0"], "DA": [], "O": {"%": 77, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_117.w_0"], "DA": [], "O": {"%": 78, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 32, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_116.b_0"], "DA": [], "O": {"%": 79, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_116.w_0"], "DA": [], "O": {"%": 80, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 32, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_115.b_0"], "DA": [], "O": {"%": 81, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_115.w_0"], "DA": [], "O": {"%": 82, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 32, 7, 7], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_114.b_0"], "DA": [], "O": {"%": 83, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_114.w_0"], "DA": [], "O": {"%": 84, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 32, 1, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_113.b_0"], "DA": [], "O": {"%": 85, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_113.w_0"], "DA": [], "O": {"%": 86, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 32, 1, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_112.b_0"], "DA": [], "O": {"%": 87, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_112.w_0"], "DA": [], "O": {"%": 88, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 32, 1, 7], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_111.b_0"], "DA": [], "O": {"%": 89, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_111.w_0"], "DA": [], "O": {"%": 90, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 32, 3, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_110.b_0"], "DA": [], "O": {"%": 91, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_110.w_0"], "DA": [], "O": {"%": 92, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 32, 5, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_109.b_0"], "DA": [], "O": {"%": 93, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_109.w_0"], "DA": [], "O": {"%": 94, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 32, 7, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_108.b_0"], "DA": [], "O": {"%": 95, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_108.w_0"], "DA": [], "O": {"%": 96, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64, 32, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_107.b_0"], "DA": [], "O": {"%": 97, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_107.w_0"], "DA": [], "O": {"%": 98, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 64, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_80.w_2"], "DA": [], "O": {"%": 99, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_80.w_1"], "DA": [], "O": {"%": 100, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_80.b_0"], "DA": [], "O": {"%": 101, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_80.w_0"], "DA": [], "O": {"%": 102, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_106.b_0"], "DA": [], "O": {"%": 103, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_106.w_0"], "DA": [], "O": {"%": 104, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 32, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_105.b_0"], "DA": [], "O": {"%": 105, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_105.w_0"], "DA": [], "O": {"%": 106, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 32, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_104.b_0"], "DA": [], "O": {"%": 107, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_104.w_0"], "DA": [], "O": {"%": 108, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 32, 7, 7], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_103.b_0"], "DA": [], "O": {"%": 109, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_103.w_0"], "DA": [], "O": {"%": 110, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 32, 1, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_102.b_0"], "DA": [], "O": {"%": 111, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_102.w_0"], "DA": [], "O": {"%": 112, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 32, 1, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_101.b_0"], "DA": [], "O": {"%": 113, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_101.w_0"], "DA": [], "O": {"%": 114, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 32, 1, 7], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_100.b_0"], "DA": [], "O": {"%": 115, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_100.w_0"], "DA": [], "O": {"%": 116, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 32, 3, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_99.b_0"], "DA": [], "O": {"%": 117, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_99.w_0"], "DA": [], "O": {"%": 118, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 32, 5, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_98.b_0"], "DA": [], "O": {"%": 119, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_98.w_0"], "DA": [], "O": {"%": 120, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 32, 7, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_97.b_0"], "DA": [], "O": {"%": 121, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_97.w_0"], "DA": [], "O": {"%": 122, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64, 32, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_96.b_0"], "DA": [], "O": {"%": 123, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_96.w_0"], "DA": [], "O": {"%": 124, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 64, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_95.w_0"], "DA": [], "O": {"%": 125, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64, 64, 9, 9], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_91.w_0"], "DA": [], "O": {"%": 126, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64, 64, 9, 9], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_87.w_0"], "DA": [], "O": {"%": 127, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64, 64, 9, 9], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_83.w_0"], "DA": [], "O": {"%": 128, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64, 64, 9, 9], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_94.w_0"], "DA": [], "O": {"%": 129, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64, 64, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_90.w_0"], "DA": [], "O": {"%": 130, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64, 64, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_86.w_0"], "DA": [], "O": {"%": 131, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64, 64, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_93.w_0"], "DA": [], "O": {"%": 132, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64, 256, 9, 9], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_89.w_0"], "DA": [], "O": {"%": 133, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64, 256, 9, 9], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_85.w_0"], "DA": [], "O": {"%": 134, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64, 256, 9, 9], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_82.w_0"], "DA": [], "O": {"%": 135, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64, 256, 9, 9], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_92.w_0"], "DA": [], "O": {"%": 136, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256, 2048, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_88.w_0"], "DA": [], "O": {"%": 137, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256, 1024, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_84.w_0"], "DA": [], "O": {"%": 138, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256, 512, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_81.w_0"], "DA": [], "O": {"%": 139, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256, 128, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_79.w_2"], "DA": [], "O": {"%": 140, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [2048], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_79.w_1"], "DA": [], "O": {"%": 141, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [2048], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_79.b_0"], "DA": [], "O": {"%": 142, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [2048], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_79.w_0"], "DA": [], "O": {"%": 143, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [2048], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_79.w_0"], "DA": [], "O": {"%": 144, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [2048, 1024, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_78.w_2"], "DA": [], "O": {"%": 145, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_78.w_1"], "DA": [], "O": {"%": 146, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_78.b_0"], "DA": [], "O": {"%": 147, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_78.w_0"], "DA": [], "O": {"%": 148, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_78.w_0"], "DA": [], "O": {"%": 149, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024, 3328, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_77.w_2"], "DA": [], "O": {"%": 150, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_77.w_1"], "DA": [], "O": {"%": 151, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_77.b_0"], "DA": [], "O": {"%": 152, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_77.w_0"], "DA": [], "O": {"%": 153, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_77.w_0"], "DA": [], "O": {"%": 154, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_76.w_2"], "DA": [], "O": {"%": 155, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_76.w_1"], "DA": [], "O": {"%": 156, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_76.b_0"], "DA": [], "O": {"%": 157, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_76.w_0"], "DA": [], "O": {"%": 158, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_76.w_0"], "DA": [], "O": {"%": 159, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384, 384, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_75.w_2"], "DA": [], "O": {"%": 160, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_75.w_1"], "DA": [], "O": {"%": 161, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_75.b_0"], "DA": [], "O": {"%": 162, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_75.w_0"], "DA": [], "O": {"%": 163, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_75.w_0"], "DA": [], "O": {"%": 164, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_74.w_2"], "DA": [], "O": {"%": 165, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_74.w_1"], "DA": [], "O": {"%": 166, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_74.b_0"], "DA": [], "O": {"%": 167, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_74.w_0"], "DA": [], "O": {"%": 168, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_74.w_0"], "DA": [], "O": {"%": 169, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384, 384, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_73.w_2"], "DA": [], "O": {"%": 170, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_73.w_1"], "DA": [], "O": {"%": 171, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_73.b_0"], "DA": [], "O": {"%": 172, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_73.w_0"], "DA": [], "O": {"%": 173, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_73.w_0"], "DA": [], "O": {"%": 174, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_72.w_2"], "DA": [], "O": {"%": 175, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_72.w_1"], "DA": [], "O": {"%": 176, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_72.b_0"], "DA": [], "O": {"%": 177, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_72.w_0"], "DA": [], "O": {"%": 178, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_72.w_0"], "DA": [], "O": {"%": 179, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384, 384, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_71.w_2"], "DA": [], "O": {"%": 180, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_71.w_1"], "DA": [], "O": {"%": 181, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_71.b_0"], "DA": [], "O": {"%": 182, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_71.w_0"], "DA": [], "O": {"%": 183, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_71.w_0"], "DA": [], "O": {"%": 184, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_70.w_2"], "DA": [], "O": {"%": 185, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_70.w_1"], "DA": [], "O": {"%": 186, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_70.b_0"], "DA": [], "O": {"%": 187, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_70.w_0"], "DA": [], "O": {"%": 188, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_70.w_0"], "DA": [], "O": {"%": 189, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384, 384, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_69.w_2"], "DA": [], "O": {"%": 190, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_69.w_1"], "DA": [], "O": {"%": 191, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_69.b_0"], "DA": [], "O": {"%": 192, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_69.w_0"], "DA": [], "O": {"%": 193, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_69.w_0"], "DA": [], "O": {"%": 194, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_68.w_2"], "DA": [], "O": {"%": 195, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_68.w_1"], "DA": [], "O": {"%": 196, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_68.b_0"], "DA": [], "O": {"%": 197, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_68.w_0"], "DA": [], "O": {"%": 198, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_68.w_0"], "DA": [], "O": {"%": 199, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384, 384, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_67.w_2"], "DA": [], "O": {"%": 200, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_67.w_1"], "DA": [], "O": {"%": 201, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_67.b_0"], "DA": [], "O": {"%": 202, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_67.w_0"], "DA": [], "O": {"%": 203, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_67.w_0"], "DA": [], "O": {"%": 204, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_66.w_2"], "DA": [], "O": {"%": 205, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_66.w_1"], "DA": [], "O": {"%": 206, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_66.b_0"], "DA": [], "O": {"%": 207, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_66.w_0"], "DA": [], "O": {"%": 208, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_66.w_0"], "DA": [], "O": {"%": 209, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384, 1024, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_65.w_2"], "DA": [], "O": {"%": 210, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_65.w_1"], "DA": [], "O": {"%": 211, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_65.b_0"], "DA": [], "O": {"%": 212, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_65.w_0"], "DA": [], "O": {"%": 213, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_65.w_0"], "DA": [], "O": {"%": 214, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024, 1, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_64.w_2"], "DA": [], "O": {"%": 215, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_64.w_1"], "DA": [], "O": {"%": 216, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_64.b_0"], "DA": [], "O": {"%": 217, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_64.w_0"], "DA": [], "O": {"%": 218, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_64.w_0"], "DA": [], "O": {"%": 219, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024, 512, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_63.w_2"], "DA": [], "O": {"%": 220, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_63.w_1"], "DA": [], "O": {"%": 221, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_63.b_0"], "DA": [], "O": {"%": 222, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_63.w_0"], "DA": [], "O": {"%": 223, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_63.w_0"], "DA": [], "O": {"%": 224, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512, 2176, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_62.w_2"], "DA": [], "O": {"%": 225, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_62.w_1"], "DA": [], "O": {"%": 226, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_62.b_0"], "DA": [], "O": {"%": 227, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_62.w_0"], "DA": [], "O": {"%": 228, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_62.w_0"], "DA": [], "O": {"%": 229, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_61.w_2"], "DA": [], "O": {"%": 230, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_61.w_1"], "DA": [], "O": {"%": 231, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_61.b_0"], "DA": [], "O": {"%": 232, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_61.w_0"], "DA": [], "O": {"%": 233, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_61.w_0"], "DA": [], "O": {"%": 234, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 192, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_60.w_2"], "DA": [], "O": {"%": 235, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_60.w_1"], "DA": [], "O": {"%": 236, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_60.b_0"], "DA": [], "O": {"%": 237, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_60.w_0"], "DA": [], "O": {"%": 238, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_60.w_0"], "DA": [], "O": {"%": 239, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_59.w_2"], "DA": [], "O": {"%": 240, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_59.w_1"], "DA": [], "O": {"%": 241, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_59.b_0"], "DA": [], "O": {"%": 242, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_59.w_0"], "DA": [], "O": {"%": 243, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_59.w_0"], "DA": [], "O": {"%": 244, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 192, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_58.w_2"], "DA": [], "O": {"%": 245, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_58.w_1"], "DA": [], "O": {"%": 246, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_58.b_0"], "DA": [], "O": {"%": 247, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_58.w_0"], "DA": [], "O": {"%": 248, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_58.w_0"], "DA": [], "O": {"%": 249, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_57.w_2"], "DA": [], "O": {"%": 250, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_57.w_1"], "DA": [], "O": {"%": 251, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_57.b_0"], "DA": [], "O": {"%": 252, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_57.w_0"], "DA": [], "O": {"%": 253, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_57.w_0"], "DA": [], "O": {"%": 254, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 192, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_56.w_2"], "DA": [], "O": {"%": 255, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_56.w_1"], "DA": [], "O": {"%": 256, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_56.b_0"], "DA": [], "O": {"%": 257, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_56.w_0"], "DA": [], "O": {"%": 258, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_56.w_0"], "DA": [], "O": {"%": 259, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_55.w_2"], "DA": [], "O": {"%": 260, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_55.w_1"], "DA": [], "O": {"%": 261, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_55.b_0"], "DA": [], "O": {"%": 262, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_55.w_0"], "DA": [], "O": {"%": 263, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_55.w_0"], "DA": [], "O": {"%": 264, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 192, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_54.w_2"], "DA": [], "O": {"%": 265, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_54.w_1"], "DA": [], "O": {"%": 266, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_54.b_0"], "DA": [], "O": {"%": 267, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_54.w_0"], "DA": [], "O": {"%": 268, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_54.w_0"], "DA": [], "O": {"%": 269, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_53.w_2"], "DA": [], "O": {"%": 270, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_53.w_1"], "DA": [], "O": {"%": 271, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_53.b_0"], "DA": [], "O": {"%": 272, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_53.w_0"], "DA": [], "O": {"%": 273, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_53.w_0"], "DA": [], "O": {"%": 274, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 192, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_52.w_2"], "DA": [], "O": {"%": 275, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_52.w_1"], "DA": [], "O": {"%": 276, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_52.b_0"], "DA": [], "O": {"%": 277, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_52.w_0"], "DA": [], "O": {"%": 278, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_52.w_0"], "DA": [], "O": {"%": 279, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_51.w_2"], "DA": [], "O": {"%": 280, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_51.w_1"], "DA": [], "O": {"%": 281, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_51.b_0"], "DA": [], "O": {"%": 282, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_51.w_0"], "DA": [], "O": {"%": 283, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_51.w_0"], "DA": [], "O": {"%": 284, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 1024, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_50.w_2"], "DA": [], "O": {"%": 285, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_50.w_1"], "DA": [], "O": {"%": 286, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_50.b_0"], "DA": [], "O": {"%": 287, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_50.w_0"], "DA": [], "O": {"%": 288, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_50.w_0"], "DA": [], "O": {"%": 289, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024, 512, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_49.w_2"], "DA": [], "O": {"%": 290, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_49.w_1"], "DA": [], "O": {"%": 291, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_49.b_0"], "DA": [], "O": {"%": 292, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_49.w_0"], "DA": [], "O": {"%": 293, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_49.w_0"], "DA": [], "O": {"%": 294, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512, 2176, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_48.w_2"], "DA": [], "O": {"%": 295, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_48.w_1"], "DA": [], "O": {"%": 296, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_48.b_0"], "DA": [], "O": {"%": 297, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_48.w_0"], "DA": [], "O": {"%": 298, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_48.w_0"], "DA": [], "O": {"%": 299, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_47.w_2"], "DA": [], "O": {"%": 300, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_47.w_1"], "DA": [], "O": {"%": 301, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_47.b_0"], "DA": [], "O": {"%": 302, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_47.w_0"], "DA": [], "O": {"%": 303, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_47.w_0"], "DA": [], "O": {"%": 304, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 192, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_46.w_2"], "DA": [], "O": {"%": 305, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_46.w_1"], "DA": [], "O": {"%": 306, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_46.b_0"], "DA": [], "O": {"%": 307, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_46.w_0"], "DA": [], "O": {"%": 308, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_46.w_0"], "DA": [], "O": {"%": 309, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_45.w_2"], "DA": [], "O": {"%": 310, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_45.w_1"], "DA": [], "O": {"%": 311, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_45.b_0"], "DA": [], "O": {"%": 312, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_45.w_0"], "DA": [], "O": {"%": 313, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_45.w_0"], "DA": [], "O": {"%": 314, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 192, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_44.w_2"], "DA": [], "O": {"%": 315, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_44.w_1"], "DA": [], "O": {"%": 316, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_44.b_0"], "DA": [], "O": {"%": 317, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_44.w_0"], "DA": [], "O": {"%": 318, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_44.w_0"], "DA": [], "O": {"%": 319, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_43.w_2"], "DA": [], "O": {"%": 320, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_43.w_1"], "DA": [], "O": {"%": 321, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_43.b_0"], "DA": [], "O": {"%": 322, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_43.w_0"], "DA": [], "O": {"%": 323, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_43.w_0"], "DA": [], "O": {"%": 324, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 192, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_42.w_2"], "DA": [], "O": {"%": 325, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_42.w_1"], "DA": [], "O": {"%": 326, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_42.b_0"], "DA": [], "O": {"%": 327, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_42.w_0"], "DA": [], "O": {"%": 328, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_42.w_0"], "DA": [], "O": {"%": 329, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_41.w_2"], "DA": [], "O": {"%": 330, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_41.w_1"], "DA": [], "O": {"%": 331, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_41.b_0"], "DA": [], "O": {"%": 332, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_41.w_0"], "DA": [], "O": {"%": 333, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_41.w_0"], "DA": [], "O": {"%": 334, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 192, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_40.w_2"], "DA": [], "O": {"%": 335, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_40.w_1"], "DA": [], "O": {"%": 336, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_40.b_0"], "DA": [], "O": {"%": 337, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_40.w_0"], "DA": [], "O": {"%": 338, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_40.w_0"], "DA": [], "O": {"%": 339, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_39.w_2"], "DA": [], "O": {"%": 340, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_39.w_1"], "DA": [], "O": {"%": 341, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_39.b_0"], "DA": [], "O": {"%": 342, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_39.w_0"], "DA": [], "O": {"%": 343, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_39.w_0"], "DA": [], "O": {"%": 344, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 192, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_38.w_2"], "DA": [], "O": {"%": 345, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_38.w_1"], "DA": [], "O": {"%": 346, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_38.b_0"], "DA": [], "O": {"%": 347, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_38.w_0"], "DA": [], "O": {"%": 348, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_38.w_0"], "DA": [], "O": {"%": 349, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_37.w_2"], "DA": [], "O": {"%": 350, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_37.w_1"], "DA": [], "O": {"%": 351, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_37.b_0"], "DA": [], "O": {"%": 352, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_37.w_0"], "DA": [], "O": {"%": 353, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_37.w_0"], "DA": [], "O": {"%": 354, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 1024, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_36.w_2"], "DA": [], "O": {"%": 355, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_36.w_1"], "DA": [], "O": {"%": 356, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_36.b_0"], "DA": [], "O": {"%": 357, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_36.w_0"], "DA": [], "O": {"%": 358, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_36.w_0"], "DA": [], "O": {"%": 359, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024, 512, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_35.w_2"], "DA": [], "O": {"%": 360, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_35.w_1"], "DA": [], "O": {"%": 361, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_35.b_0"], "DA": [], "O": {"%": 362, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_35.w_0"], "DA": [], "O": {"%": 363, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_35.w_0"], "DA": [], "O": {"%": 364, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512, 1664, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_34.w_2"], "DA": [], "O": {"%": 365, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_34.w_1"], "DA": [], "O": {"%": 366, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_34.b_0"], "DA": [], "O": {"%": 367, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_34.w_0"], "DA": [], "O": {"%": 368, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_34.w_0"], "DA": [], "O": {"%": 369, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_33.w_2"], "DA": [], "O": {"%": 370, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_33.w_1"], "DA": [], "O": {"%": 371, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_33.b_0"], "DA": [], "O": {"%": 372, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_33.w_0"], "DA": [], "O": {"%": 373, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_33.w_0"], "DA": [], "O": {"%": 374, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 192, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_32.w_2"], "DA": [], "O": {"%": 375, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_32.w_1"], "DA": [], "O": {"%": 376, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_32.b_0"], "DA": [], "O": {"%": 377, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_32.w_0"], "DA": [], "O": {"%": 378, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_32.w_0"], "DA": [], "O": {"%": 379, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_31.w_2"], "DA": [], "O": {"%": 380, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_31.w_1"], "DA": [], "O": {"%": 381, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_31.b_0"], "DA": [], "O": {"%": 382, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_31.w_0"], "DA": [], "O": {"%": 383, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_31.w_0"], "DA": [], "O": {"%": 384, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 192, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_30.w_2"], "DA": [], "O": {"%": 385, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_30.w_1"], "DA": [], "O": {"%": 386, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_30.b_0"], "DA": [], "O": {"%": 387, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_30.w_0"], "DA": [], "O": {"%": 388, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_30.w_0"], "DA": [], "O": {"%": 389, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_29.w_2"], "DA": [], "O": {"%": 390, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_29.w_1"], "DA": [], "O": {"%": 391, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_29.b_0"], "DA": [], "O": {"%": 392, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_29.w_0"], "DA": [], "O": {"%": 393, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_29.w_0"], "DA": [], "O": {"%": 394, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 192, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_28.w_2"], "DA": [], "O": {"%": 395, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_28.w_1"], "DA": [], "O": {"%": 396, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_28.b_0"], "DA": [], "O": {"%": 397, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_28.w_0"], "DA": [], "O": {"%": 398, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_28.w_0"], "DA": [], "O": {"%": 399, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_27.w_2"], "DA": [], "O": {"%": 400, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_27.w_1"], "DA": [], "O": {"%": 401, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_27.b_0"], "DA": [], "O": {"%": 402, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_27.w_0"], "DA": [], "O": {"%": 403, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_27.w_0"], "DA": [], "O": {"%": 404, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 192, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_26.w_2"], "DA": [], "O": {"%": 405, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_26.w_1"], "DA": [], "O": {"%": 406, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_26.b_0"], "DA": [], "O": {"%": 407, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_26.w_0"], "DA": [], "O": {"%": 408, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_26.w_0"], "DA": [], "O": {"%": 409, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_25.w_2"], "DA": [], "O": {"%": 410, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_25.w_1"], "DA": [], "O": {"%": 411, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_25.b_0"], "DA": [], "O": {"%": 412, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_25.w_0"], "DA": [], "O": {"%": 413, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_25.w_0"], "DA": [], "O": {"%": 414, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 192, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_24.w_2"], "DA": [], "O": {"%": 415, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_24.w_1"], "DA": [], "O": {"%": 416, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_24.b_0"], "DA": [], "O": {"%": 417, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_24.w_0"], "DA": [], "O": {"%": 418, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_24.w_0"], "DA": [], "O": {"%": 419, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 1, 5, 5], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_23.w_2"], "DA": [], "O": {"%": 420, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_23.w_1"], "DA": [], "O": {"%": 421, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_23.b_0"], "DA": [], "O": {"%": 422, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_23.w_0"], "DA": [], "O": {"%": 423, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_23.w_0"], "DA": [], "O": {"%": 424, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192, 512, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_22.w_2"], "DA": [], "O": {"%": 425, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_22.w_1"], "DA": [], "O": {"%": 426, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_22.b_0"], "DA": [], "O": {"%": 427, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_22.w_0"], "DA": [], "O": {"%": 428, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_22.w_0"], "DA": [], "O": {"%": 429, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512, 1, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_21.w_2"], "DA": [], "O": {"%": 430, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_21.w_1"], "DA": [], "O": {"%": 431, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_21.b_0"], "DA": [], "O": {"%": 432, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_21.w_0"], "DA": [], "O": {"%": 433, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_21.w_0"], "DA": [], "O": {"%": 434, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512, 256, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_20.w_2"], "DA": [], "O": {"%": 435, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_20.w_1"], "DA": [], "O": {"%": 436, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_20.b_0"], "DA": [], "O": {"%": 437, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_20.w_0"], "DA": [], "O": {"%": 438, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_20.w_0"], "DA": [], "O": {"%": 439, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256, 704, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_19.w_2"], "DA": [], "O": {"%": 440, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_19.w_1"], "DA": [], "O": {"%": 441, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_19.b_0"], "DA": [], "O": {"%": 442, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_19.w_0"], "DA": [], "O": {"%": 443, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_19.w_0"], "DA": [], "O": {"%": 444, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96, 96, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_18.w_2"], "DA": [], "O": {"%": 445, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_18.w_1"], "DA": [], "O": {"%": 446, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_18.b_0"], "DA": [], "O": {"%": 447, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_18.w_0"], "DA": [], "O": {"%": 448, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_18.w_0"], "DA": [], "O": {"%": 449, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96, 96, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_17.w_2"], "DA": [], "O": {"%": 450, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_17.w_1"], "DA": [], "O": {"%": 451, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_17.b_0"], "DA": [], "O": {"%": 452, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_17.w_0"], "DA": [], "O": {"%": 453, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_17.w_0"], "DA": [], "O": {"%": 454, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96, 96, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_16.w_2"], "DA": [], "O": {"%": 455, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_16.w_1"], "DA": [], "O": {"%": 456, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_16.b_0"], "DA": [], "O": {"%": 457, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_16.w_0"], "DA": [], "O": {"%": 458, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_16.w_0"], "DA": [], "O": {"%": 459, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96, 96, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_15.w_2"], "DA": [], "O": {"%": 460, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_15.w_1"], "DA": [], "O": {"%": 461, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_15.b_0"], "DA": [], "O": {"%": 462, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_15.w_0"], "DA": [], "O": {"%": 463, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_15.w_0"], "DA": [], "O": {"%": 464, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96, 96, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_14.w_2"], "DA": [], "O": {"%": 465, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_14.w_1"], "DA": [], "O": {"%": 466, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_14.b_0"], "DA": [], "O": {"%": 467, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_14.w_0"], "DA": [], "O": {"%": 468, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_14.w_0"], "DA": [], "O": {"%": 469, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96, 128, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_13.w_2"], "DA": [], "O": {"%": 470, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_13.w_1"], "DA": [], "O": {"%": 471, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_13.b_0"], "DA": [], "O": {"%": 472, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_13.w_0"], "DA": [], "O": {"%": 473, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_13.w_0"], "DA": [], "O": {"%": 474, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128, 1, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_12.w_2"], "DA": [], "O": {"%": 475, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_12.w_1"], "DA": [], "O": {"%": 476, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_12.b_0"], "DA": [], "O": {"%": 477, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_12.w_0"], "DA": [], "O": {"%": 478, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_12.w_0"], "DA": [], "O": {"%": 479, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128, 64, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_11.w_2"], "DA": [], "O": {"%": 480, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_11.w_1"], "DA": [], "O": {"%": 481, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_11.b_0"], "DA": [], "O": {"%": 482, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_11.w_0"], "DA": [], "O": {"%": 483, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_11.w_0"], "DA": [], "O": {"%": 484, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64, 336, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_10.w_2"], "DA": [], "O": {"%": 485, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_10.w_1"], "DA": [], "O": {"%": 486, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_10.b_0"], "DA": [], "O": {"%": 487, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_10.w_0"], "DA": [], "O": {"%": 488, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_10.w_0"], "DA": [], "O": {"%": 489, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48, 48, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_9.w_2"], "DA": [], "O": {"%": 490, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_9.w_1"], "DA": [], "O": {"%": 491, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_9.b_0"], "DA": [], "O": {"%": 492, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_9.w_0"], "DA": [], "O": {"%": 493, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_9.w_0"], "DA": [], "O": {"%": 494, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48, 48, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_8.w_2"], "DA": [], "O": {"%": 495, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_8.w_1"], "DA": [], "O": {"%": 496, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_8.b_0"], "DA": [], "O": {"%": 497, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_8.w_0"], "DA": [], "O": {"%": 498, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_8.w_0"], "DA": [], "O": {"%": 499, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48, 48, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_7.w_2"], "DA": [], "O": {"%": 500, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_7.w_1"], "DA": [], "O": {"%": 501, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_7.b_0"], "DA": [], "O": {"%": 502, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_7.w_0"], "DA": [], "O": {"%": 503, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_7.w_0"], "DA": [], "O": {"%": 504, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48, 48, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_6.w_2"], "DA": [], "O": {"%": 505, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_6.w_1"], "DA": [], "O": {"%": 506, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_6.b_0"], "DA": [], "O": {"%": 507, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_6.w_0"], "DA": [], "O": {"%": 508, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_6.w_0"], "DA": [], "O": {"%": 509, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48, 48, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_5.w_2"], "DA": [], "O": {"%": 510, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_5.w_1"], "DA": [], "O": {"%": 511, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_5.b_0"], "DA": [], "O": {"%": 512, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_5.w_0"], "DA": [], "O": {"%": 513, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_5.w_0"], "DA": [], "O": {"%": 514, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48, 48, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_4.w_2"], "DA": [], "O": {"%": 515, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_4.w_1"], "DA": [], "O": {"%": 516, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_4.b_0"], "DA": [], "O": {"%": 517, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_4.w_0"], "DA": [], "O": {"%": 518, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_4.w_0"], "DA": [], "O": {"%": 519, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48, 32, 1, 1], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_3.w_2"], "DA": [], "O": {"%": 520, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_3.w_1"], "DA": [], "O": {"%": 521, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_3.b_0"], "DA": [], "O": {"%": 522, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_3.w_0"], "DA": [], "O": {"%": 523, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_3.w_0"], "DA": [], "O": {"%": 524, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 64, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_2.w_2"], "DA": [], "O": {"%": 525, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_2.w_1"], "DA": [], "O": {"%": 526, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_2.b_0"], "DA": [], "O": {"%": 527, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_2.w_0"], "DA": [], "O": {"%": 528, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_2.w_0"], "DA": [], "O": {"%": 529, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 16, 2, 2], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_1.w_2"], "DA": [], "O": {"%": 530, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_1.w_1"], "DA": [], "O": {"%": 531, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_1.b_0"], "DA": [], "O": {"%": 532, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_1.w_0"], "DA": [], "O": {"%": 533, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_1.w_0"], "DA": [], "O": {"%": 534, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16, 32, 2, 2], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_0.w_2"], "DA": [], "O": {"%": 535, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_0.w_1"], "DA": [], "O": {"%": 536, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 1, 0], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_0.b_0"], "DA": [], "O": {"%": 537, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "batch_norm2d_0.w_0"], "DA": [], "O": {"%": 538, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "p", "A": [0, 1, 1, "conv2d_0.w_0"], "DA": [], "O": {"%": 539, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32, 3, 3, 3], "NCHW", [], 0]}}, "OA": [1, 0, 1], "QA": []}, {"#": "1.data", "A": [{"AT": {"#": "0.a_str", "D": "x"}, "N": "name"}, {"AT": {"#": "1.a_intarray", "D": [-1, 3, -1, -1]}, "N": "shape"}, {"AT": {"#": "1.a_dtype", "D": "float32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [0, 0, ""]}, "N": "place"}], "I": [], "O": [{"%": 540, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 3, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/StemBlock/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 540}, {"%": 539}], "O": [{"%": 541, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/StemBlock/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 541}, {"%": 536}, {"%": 535}, {"%": 538}, {"%": 537}], "O": [{"%": 542, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}, {"%": 543, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 544, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 545, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 546, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 547, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/StemBlock/ConvBNAct/ReLU/"}, "N": "struct_name"}], "I": [{"%": 542}], "O": [{"%": 548, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "SAME"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/StemBlock/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 548}, {"%": 534}], "O": [{"%": 549, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 16, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/StemBlock/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 549}, {"%": 531}, {"%": 530}, {"%": 533}, {"%": 532}], "O": [{"%": 550, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 16, -1, -1], "NCHW", [], 0]}}, {"%": 551, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, {"%": 552, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, {"%": 553, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, {"%": 554, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [16], "NCHW", [], 0]}}, {"%": 555, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/StemBlock/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 550}], "O": [{"%": 556, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 16, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "SAME"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/StemBlock/ConvBNAct_2/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 556}, {"%": 529}], "O": [{"%": 557, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/StemBlock/ConvBNAct_2/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 557}, {"%": 526}, {"%": 525}, {"%": 528}, {"%": 527}], "O": [{"%": 558, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}, {"%": 559, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 560, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 561, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 562, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 563, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/StemBlock/ConvBNAct_2/ReLU/"}, "N": "struct_name"}], "I": [{"%": 558}], "O": [{"%": 564, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 2}, {"#": "0.a_i64", "D": 2}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/StemBlock/MaxPool2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 565, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [2], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.pool2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "ceil_mode"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "exclusive"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "max"}, "N": "pooling_type"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "global_pooling"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "adaptive"}, {"AT": {"#": "0.a_str", "D": "SAME"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/StemBlock/MaxPool2D/"}, "N": "struct_name"}], "I": [{"%": 548}, {"%": 565}], "O": [{"%": 566, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 1.0}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/StemBlock/"}, "N": "struct_name"}], "I": [], "O": [{"%": 567, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "0.combine", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/StemBlock/"}, "N": "struct_name"}], "I": [{"%": 566}, {"%": 564}], "O": [{"%": 568, "TT": {"#": "0.t_vec", "D": [{"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.concat", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/StemBlock/"}, "N": "struct_name"}], "I": [{"%": 568}, {"%": 567}], "O": [{"%": 569, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/StemBlock/ConvBNAct_3/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 569}, {"%": 524}], "O": [{"%": 570, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/StemBlock/ConvBNAct_3/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 570}, {"%": 521}, {"%": 520}, {"%": 523}, {"%": 522}], "O": [{"%": 571, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}, {"%": 572, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 573, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 574, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 575, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [32], "NCHW", [], 0]}}, {"%": 576, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/StemBlock/ConvBNAct_3/ReLU/"}, "N": "struct_name"}], "I": [{"%": 571}], "O": [{"%": 577, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/StemBlock/ConvBNAct_4/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 577}, {"%": 519}], "O": [{"%": 578, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/StemBlock/ConvBNAct_4/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 578}, {"%": 516}, {"%": 515}, {"%": 518}, {"%": 517}], "O": [{"%": 579, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, -1, -1], "NCHW", [], 0]}}, {"%": 580, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 581, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 582, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 583, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 584, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/StemBlock/ConvBNAct_4/ReLU/"}, "N": "struct_name"}], "I": [{"%": 579}], "O": [{"%": 585, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/Sequential/HGV2_Block/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 585}, {"%": 514}], "O": [{"%": 586, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/Sequential/HGV2_Block/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 586}, {"%": 511}, {"%": 510}, {"%": 513}, {"%": 512}], "O": [{"%": 587, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, -1, -1], "NCHW", [], 0]}}, {"%": 588, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 589, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 590, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 591, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 592, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/Sequential/HGV2_Block/ConvBNAct/ReLU/"}, "N": "struct_name"}], "I": [{"%": 587}], "O": [{"%": 593, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/Sequential/HGV2_Block/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 593}, {"%": 509}], "O": [{"%": 594, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/Sequential/HGV2_Block/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 594}, {"%": 506}, {"%": 505}, {"%": 508}, {"%": 507}], "O": [{"%": 595, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, -1, -1], "NCHW", [], 0]}}, {"%": 596, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 597, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 598, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 599, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 600, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/Sequential/HGV2_Block/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 595}], "O": [{"%": 601, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/Sequential/HGV2_Block/ConvBNAct_2/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 601}, {"%": 504}], "O": [{"%": 602, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/Sequential/HGV2_Block/ConvBNAct_2/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 602}, {"%": 501}, {"%": 500}, {"%": 503}, {"%": 502}], "O": [{"%": 603, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, -1, -1], "NCHW", [], 0]}}, {"%": 604, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 605, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 606, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 607, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 608, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/Sequential/HGV2_Block/ConvBNAct_2/ReLU/"}, "N": "struct_name"}], "I": [{"%": 603}], "O": [{"%": 609, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/Sequential/HGV2_Block/ConvBNAct_3/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 609}, {"%": 499}], "O": [{"%": 610, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/Sequential/HGV2_Block/ConvBNAct_3/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 610}, {"%": 496}, {"%": 495}, {"%": 498}, {"%": 497}], "O": [{"%": 611, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, -1, -1], "NCHW", [], 0]}}, {"%": 612, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 613, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 614, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 615, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 616, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/Sequential/HGV2_Block/ConvBNAct_3/ReLU/"}, "N": "struct_name"}], "I": [{"%": 611}], "O": [{"%": 617, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/Sequential/HGV2_Block/ConvBNAct_4/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 617}, {"%": 494}], "O": [{"%": 618, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/Sequential/HGV2_Block/ConvBNAct_4/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 618}, {"%": 491}, {"%": 490}, {"%": 493}, {"%": 492}], "O": [{"%": 619, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, -1, -1], "NCHW", [], 0]}}, {"%": 620, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 621, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 622, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 623, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 624, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/Sequential/HGV2_Block/ConvBNAct_4/ReLU/"}, "N": "struct_name"}], "I": [{"%": 619}], "O": [{"%": 625, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/Sequential/HGV2_Block/ConvBNAct_5/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 625}, {"%": 489}], "O": [{"%": 626, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/Sequential/HGV2_Block/ConvBNAct_5/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 626}, {"%": 486}, {"%": 485}, {"%": 488}, {"%": 487}], "O": [{"%": 627, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, -1, -1], "NCHW", [], 0]}}, {"%": 628, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 629, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 630, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 631, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [48], "NCHW", [], 0]}}, {"%": 632, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/Sequential/HGV2_Block/ConvBNAct_5/ReLU/"}, "N": "struct_name"}], "I": [{"%": 627}], "O": [{"%": 633, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 1.0}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/Sequential/HGV2_Block/"}, "N": "struct_name"}], "I": [], "O": [{"%": 634, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "0.combine", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/Sequential/HGV2_Block/"}, "N": "struct_name"}], "I": [{"%": 585}, {"%": 593}, {"%": 601}, {"%": 609}, {"%": 617}, {"%": 625}, {"%": 633}], "O": [{"%": 635, "TT": {"#": "0.t_vec", "D": [{"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, -1, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, -1, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, -1, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, -1, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, -1, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, -1, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 48, -1, -1], "NCHW", [], 0]}]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.concat", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/Sequential/HGV2_Block/"}, "N": "struct_name"}], "I": [{"%": 635}, {"%": 634}], "O": [{"%": 636, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 336, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/Sequential/HGV2_Block/ConvBNAct_6/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 636}, {"%": 484}], "O": [{"%": 637, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/Sequential/HGV2_Block/ConvBNAct_6/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 637}, {"%": 481}, {"%": 480}, {"%": 483}, {"%": 482}], "O": [{"%": 638, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}}, {"%": 639, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 640, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 641, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 642, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 643, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/Sequential/HGV2_Block/ConvBNAct_6/ReLU/"}, "N": "struct_name"}], "I": [{"%": 638}], "O": [{"%": 644, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/Sequential/HGV2_Block/ConvBNAct_7/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 644}, {"%": 479}], "O": [{"%": 645, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/Sequential/HGV2_Block/ConvBNAct_7/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 645}, {"%": 476}, {"%": 475}, {"%": 478}, {"%": 477}], "O": [{"%": 646, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, -1, -1], "NCHW", [], 0]}}, {"%": 647, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 648, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 649, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 650, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 651, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage/Sequential/HGV2_Block/ConvBNAct_7/ReLU/"}, "N": "struct_name"}], "I": [{"%": 646}], "O": [{"%": 652, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 128}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 652}, {"%": 474}], "O": [{"%": 653, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 653}, {"%": 471}, {"%": 470}, {"%": 473}, {"%": 472}], "O": [{"%": 654, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, -1, -1], "NCHW", [], 0]}}, {"%": 655, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 656, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 657, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 658, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [128], "NCHW", [], 0]}}, {"%": 659, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/Sequential/HGV2_Block/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 654}, {"%": 469}], "O": [{"%": 660, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/Sequential/HGV2_Block/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 660}, {"%": 466}, {"%": 465}, {"%": 468}, {"%": 467}], "O": [{"%": 661, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}, {"%": 662, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, {"%": 663, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, {"%": 664, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, {"%": 665, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, {"%": 666, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/Sequential/HGV2_Block/ConvBNAct/ReLU/"}, "N": "struct_name"}], "I": [{"%": 661}], "O": [{"%": 667, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/Sequential/HGV2_Block/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 667}, {"%": 464}], "O": [{"%": 668, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/Sequential/HGV2_Block/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 668}, {"%": 461}, {"%": 460}, {"%": 463}, {"%": 462}], "O": [{"%": 669, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}, {"%": 670, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, {"%": 671, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, {"%": 672, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, {"%": 673, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, {"%": 674, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/Sequential/HGV2_Block/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 669}], "O": [{"%": 675, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/Sequential/HGV2_Block/ConvBNAct_2/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 675}, {"%": 459}], "O": [{"%": 676, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/Sequential/HGV2_Block/ConvBNAct_2/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 676}, {"%": 456}, {"%": 455}, {"%": 458}, {"%": 457}], "O": [{"%": 677, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}, {"%": 678, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, {"%": 679, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, {"%": 680, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, {"%": 681, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, {"%": 682, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/Sequential/HGV2_Block/ConvBNAct_2/ReLU/"}, "N": "struct_name"}], "I": [{"%": 677}], "O": [{"%": 683, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/Sequential/HGV2_Block/ConvBNAct_3/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 683}, {"%": 454}], "O": [{"%": 684, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/Sequential/HGV2_Block/ConvBNAct_3/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 684}, {"%": 451}, {"%": 450}, {"%": 453}, {"%": 452}], "O": [{"%": 685, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}, {"%": 686, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, {"%": 687, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, {"%": 688, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, {"%": 689, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, {"%": 690, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/Sequential/HGV2_Block/ConvBNAct_3/ReLU/"}, "N": "struct_name"}], "I": [{"%": 685}], "O": [{"%": 691, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/Sequential/HGV2_Block/ConvBNAct_4/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 691}, {"%": 449}], "O": [{"%": 692, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/Sequential/HGV2_Block/ConvBNAct_4/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 692}, {"%": 446}, {"%": 445}, {"%": 448}, {"%": 447}], "O": [{"%": 693, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}, {"%": 694, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, {"%": 695, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, {"%": 696, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, {"%": 697, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, {"%": 698, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/Sequential/HGV2_Block/ConvBNAct_4/ReLU/"}, "N": "struct_name"}], "I": [{"%": 693}], "O": [{"%": 699, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/Sequential/HGV2_Block/ConvBNAct_5/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 699}, {"%": 444}], "O": [{"%": 700, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/Sequential/HGV2_Block/ConvBNAct_5/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 700}, {"%": 441}, {"%": 440}, {"%": 443}, {"%": 442}], "O": [{"%": 701, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}, {"%": 702, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, {"%": 703, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, {"%": 704, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, {"%": 705, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [96], "NCHW", [], 0]}}, {"%": 706, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/Sequential/HGV2_Block/ConvBNAct_5/ReLU/"}, "N": "struct_name"}], "I": [{"%": 701}], "O": [{"%": 707, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 1.0}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/Sequential/HGV2_Block/"}, "N": "struct_name"}], "I": [], "O": [{"%": 708, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "0.combine", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/Sequential/HGV2_Block/"}, "N": "struct_name"}], "I": [{"%": 654}, {"%": 667}, {"%": 675}, {"%": 683}, {"%": 691}, {"%": 699}, {"%": 707}], "O": [{"%": 709, "TT": {"#": "0.t_vec", "D": [{"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 128, -1, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 96, -1, -1], "NCHW", [], 0]}]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.concat", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/Sequential/HGV2_Block/"}, "N": "struct_name"}], "I": [{"%": 709}, {"%": 708}], "O": [{"%": 710, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 704, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/Sequential/HGV2_Block/ConvBNAct_6/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 710}, {"%": 439}], "O": [{"%": 711, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 256, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/Sequential/HGV2_Block/ConvBNAct_6/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 711}, {"%": 436}, {"%": 435}, {"%": 438}, {"%": 437}], "O": [{"%": 712, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 256, -1, -1], "NCHW", [], 0]}}, {"%": 713, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256], "NCHW", [], 0]}}, {"%": 714, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256], "NCHW", [], 0]}}, {"%": 715, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256], "NCHW", [], 0]}}, {"%": 716, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [256], "NCHW", [], 0]}}, {"%": 717, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/Sequential/HGV2_Block/ConvBNAct_6/ReLU/"}, "N": "struct_name"}], "I": [{"%": 712}], "O": [{"%": 718, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 256, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/Sequential/HGV2_Block/ConvBNAct_7/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 718}, {"%": 434}], "O": [{"%": 719, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 512, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/Sequential/HGV2_Block/ConvBNAct_7/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 719}, {"%": 431}, {"%": 430}, {"%": 433}, {"%": 432}], "O": [{"%": 720, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 512, -1, -1], "NCHW", [], 0]}}, {"%": 721, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, {"%": 722, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, {"%": 723, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, {"%": 724, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, {"%": 725, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_1/Sequential/HGV2_Block/ConvBNAct_7/ReLU/"}, "N": "struct_name"}], "I": [{"%": 720}], "O": [{"%": 726, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 512, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 512}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 726}, {"%": 429}], "O": [{"%": 727, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 512, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 727}, {"%": 426}, {"%": 425}, {"%": 428}, {"%": 427}], "O": [{"%": 728, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 512, -1, -1], "NCHW", [], 0]}}, {"%": 729, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, {"%": 730, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, {"%": 731, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, {"%": 732, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, {"%": 733, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 728}, {"%": 424}], "O": [{"%": 734, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 734}, {"%": 421}, {"%": 420}, {"%": 423}, {"%": 422}], "O": [{"%": 735, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}, {"%": 736, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 737, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 738, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 739, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 740, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 192}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 735}, {"%": 419}], "O": [{"%": 741, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 741}, {"%": 416}, {"%": 415}, {"%": 418}, {"%": 417}], "O": [{"%": 742, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}, {"%": 743, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 744, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 745, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 746, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 747, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 742}], "O": [{"%": 748, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct_1/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 748}, {"%": 414}], "O": [{"%": 749, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct_1/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 749}, {"%": 411}, {"%": 410}, {"%": 413}, {"%": 412}], "O": [{"%": 750, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}, {"%": 751, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 752, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 753, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 754, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 755, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 192}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct_1/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 750}, {"%": 409}], "O": [{"%": 756, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct_1/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 756}, {"%": 406}, {"%": 405}, {"%": 408}, {"%": 407}], "O": [{"%": 757, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}, {"%": 758, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 759, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 760, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 761, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 762, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct_1/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 757}], "O": [{"%": 763, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct_2/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 763}, {"%": 404}], "O": [{"%": 764, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct_2/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 764}, {"%": 401}, {"%": 400}, {"%": 403}, {"%": 402}], "O": [{"%": 765, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}, {"%": 766, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 767, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 768, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 769, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 770, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 192}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct_2/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 765}, {"%": 399}], "O": [{"%": 771, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct_2/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 771}, {"%": 396}, {"%": 395}, {"%": 398}, {"%": 397}], "O": [{"%": 772, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}, {"%": 773, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 774, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 775, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 776, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 777, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct_2/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 772}], "O": [{"%": 778, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct_3/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 778}, {"%": 394}], "O": [{"%": 779, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct_3/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 779}, {"%": 391}, {"%": 390}, {"%": 393}, {"%": 392}], "O": [{"%": 780, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}, {"%": 781, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 782, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 783, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 784, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 785, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 192}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct_3/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 780}, {"%": 389}], "O": [{"%": 786, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct_3/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 786}, {"%": 386}, {"%": 385}, {"%": 388}, {"%": 387}], "O": [{"%": 787, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}, {"%": 788, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 789, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 790, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 791, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 792, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct_3/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 787}], "O": [{"%": 793, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct_4/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 793}, {"%": 384}], "O": [{"%": 794, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct_4/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 794}, {"%": 381}, {"%": 380}, {"%": 383}, {"%": 382}], "O": [{"%": 795, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}, {"%": 796, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 797, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 798, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 799, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 800, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 192}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct_4/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 795}, {"%": 379}], "O": [{"%": 801, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct_4/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 801}, {"%": 376}, {"%": 375}, {"%": 378}, {"%": 377}], "O": [{"%": 802, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}, {"%": 803, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 804, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 805, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 806, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 807, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct_4/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 802}], "O": [{"%": 808, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct_5/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 808}, {"%": 374}], "O": [{"%": 809, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct_5/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 809}, {"%": 371}, {"%": 370}, {"%": 373}, {"%": 372}], "O": [{"%": 810, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}, {"%": 811, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 812, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 813, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 814, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 815, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 192}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct_5/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 810}, {"%": 369}], "O": [{"%": 816, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct_5/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 816}, {"%": 366}, {"%": 365}, {"%": 368}, {"%": 367}], "O": [{"%": 817, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}, {"%": 818, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 819, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 820, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 821, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 822, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/LightConvBNAct_5/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 817}], "O": [{"%": 823, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 1.0}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/"}, "N": "struct_name"}], "I": [], "O": [{"%": 824, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "0.combine", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/"}, "N": "struct_name"}], "I": [{"%": 728}, {"%": 748}, {"%": 763}, {"%": 778}, {"%": 793}, {"%": 808}, {"%": 823}], "O": [{"%": 825, "TT": {"#": "0.t_vec", "D": [{"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 512, -1, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.concat", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/"}, "N": "struct_name"}], "I": [{"%": 825}, {"%": 824}], "O": [{"%": 826, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1664, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 826}, {"%": 364}], "O": [{"%": 827, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 512, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 827}, {"%": 361}, {"%": 360}, {"%": 363}, {"%": 362}], "O": [{"%": 828, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 512, -1, -1], "NCHW", [], 0]}}, {"%": 829, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, {"%": 830, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, {"%": 831, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, {"%": 832, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, {"%": 833, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/ConvBNAct/ReLU/"}, "N": "struct_name"}], "I": [{"%": 828}], "O": [{"%": 834, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 512, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 834}, {"%": 359}], "O": [{"%": 835, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1024, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 835}, {"%": 356}, {"%": 355}, {"%": 358}, {"%": 357}], "O": [{"%": 836, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1024, -1, -1], "NCHW", [], 0]}}, {"%": 837, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, {"%": 838, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, {"%": 839, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, {"%": 840, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, {"%": 841, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 836}], "O": [{"%": 842, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1024, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 842}, {"%": 354}], "O": [{"%": 843, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 843}, {"%": 351}, {"%": 350}, {"%": 353}, {"%": 352}], "O": [{"%": 844, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}, {"%": 845, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 846, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 847, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 848, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 849, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 192}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 844}, {"%": 349}], "O": [{"%": 850, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 850}, {"%": 346}, {"%": 345}, {"%": 348}, {"%": 347}], "O": [{"%": 851, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}, {"%": 852, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 853, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 854, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 855, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 856, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 851}], "O": [{"%": 857, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct_1/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 857}, {"%": 344}], "O": [{"%": 858, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct_1/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 858}, {"%": 341}, {"%": 340}, {"%": 343}, {"%": 342}], "O": [{"%": 859, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}, {"%": 860, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 861, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 862, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 863, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 864, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 192}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct_1/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 859}, {"%": 339}], "O": [{"%": 865, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct_1/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 865}, {"%": 336}, {"%": 335}, {"%": 338}, {"%": 337}], "O": [{"%": 866, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}, {"%": 867, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 868, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 869, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 870, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 871, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct_1/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 866}], "O": [{"%": 872, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct_2/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 872}, {"%": 334}], "O": [{"%": 873, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct_2/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 873}, {"%": 331}, {"%": 330}, {"%": 333}, {"%": 332}], "O": [{"%": 874, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}, {"%": 875, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 876, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 877, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 878, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 879, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 192}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct_2/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 874}, {"%": 329}], "O": [{"%": 880, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct_2/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 880}, {"%": 326}, {"%": 325}, {"%": 328}, {"%": 327}], "O": [{"%": 881, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}, {"%": 882, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 883, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 884, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 885, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 886, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct_2/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 881}], "O": [{"%": 887, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct_3/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 887}, {"%": 324}], "O": [{"%": 888, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct_3/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 888}, {"%": 321}, {"%": 320}, {"%": 323}, {"%": 322}], "O": [{"%": 889, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}, {"%": 890, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 891, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 892, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 893, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 894, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 192}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct_3/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 889}, {"%": 319}], "O": [{"%": 895, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct_3/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 895}, {"%": 316}, {"%": 315}, {"%": 318}, {"%": 317}], "O": [{"%": 896, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}, {"%": 897, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 898, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 899, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 900, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 901, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct_3/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 896}], "O": [{"%": 902, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct_4/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 902}, {"%": 314}], "O": [{"%": 903, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct_4/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 903}, {"%": 311}, {"%": 310}, {"%": 313}, {"%": 312}], "O": [{"%": 904, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}, {"%": 905, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 906, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 907, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 908, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 909, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 192}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct_4/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 904}, {"%": 309}], "O": [{"%": 910, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct_4/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 910}, {"%": 306}, {"%": 305}, {"%": 308}, {"%": 307}], "O": [{"%": 911, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}, {"%": 912, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 913, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 914, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 915, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 916, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct_4/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 911}], "O": [{"%": 917, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct_5/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 917}, {"%": 304}], "O": [{"%": 918, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct_5/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 918}, {"%": 301}, {"%": 300}, {"%": 303}, {"%": 302}], "O": [{"%": 919, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}, {"%": 920, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 921, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 922, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 923, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 924, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 192}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct_5/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 919}, {"%": 299}], "O": [{"%": 925, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct_5/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 925}, {"%": 296}, {"%": 295}, {"%": 298}, {"%": 297}], "O": [{"%": 926, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}, {"%": 927, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 928, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 929, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 930, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 931, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/LightConvBNAct_5/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 926}], "O": [{"%": 932, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 1.0}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/"}, "N": "struct_name"}], "I": [], "O": [{"%": 933, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "0.combine", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/"}, "N": "struct_name"}], "I": [{"%": 842}, {"%": 857}, {"%": 872}, {"%": 887}, {"%": 902}, {"%": 917}, {"%": 932}], "O": [{"%": 934, "TT": {"#": "0.t_vec", "D": [{"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1024, -1, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.concat", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/"}, "N": "struct_name"}], "I": [{"%": 934}, {"%": 933}], "O": [{"%": 935, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 2176, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 935}, {"%": 294}], "O": [{"%": 936, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 512, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 936}, {"%": 291}, {"%": 290}, {"%": 293}, {"%": 292}], "O": [{"%": 937, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 512, -1, -1], "NCHW", [], 0]}}, {"%": 938, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, {"%": 939, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, {"%": 940, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, {"%": 941, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, {"%": 942, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/ConvBNAct/ReLU/"}, "N": "struct_name"}], "I": [{"%": 937}], "O": [{"%": 943, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 512, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 943}, {"%": 289}], "O": [{"%": 944, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1024, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 944}, {"%": 286}, {"%": 285}, {"%": 288}, {"%": 287}], "O": [{"%": 945, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1024, -1, -1], "NCHW", [], 0]}}, {"%": 946, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, {"%": 947, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, {"%": 948, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, {"%": 949, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, {"%": 950, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 945}], "O": [{"%": 951, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1024, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_1/"}, "N": "struct_name"}], "I": [{"%": 951}, {"%": 842}], "O": [{"%": 952, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1024, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 952}, {"%": 284}], "O": [{"%": 953, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 953}, {"%": 281}, {"%": 280}, {"%": 283}, {"%": 282}], "O": [{"%": 954, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}, {"%": 955, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 956, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 957, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 958, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 959, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 192}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 954}, {"%": 279}], "O": [{"%": 960, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 960}, {"%": 276}, {"%": 275}, {"%": 278}, {"%": 277}], "O": [{"%": 961, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}, {"%": 962, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 963, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 964, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 965, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 966, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 961}], "O": [{"%": 967, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct_1/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 967}, {"%": 274}], "O": [{"%": 968, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct_1/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 968}, {"%": 271}, {"%": 270}, {"%": 273}, {"%": 272}], "O": [{"%": 969, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}, {"%": 970, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 971, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 972, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 973, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 974, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 192}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct_1/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 969}, {"%": 269}], "O": [{"%": 975, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct_1/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 975}, {"%": 266}, {"%": 265}, {"%": 268}, {"%": 267}], "O": [{"%": 976, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}, {"%": 977, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 978, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 979, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 980, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 981, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct_1/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 976}], "O": [{"%": 982, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct_2/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 982}, {"%": 264}], "O": [{"%": 983, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct_2/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 983}, {"%": 261}, {"%": 260}, {"%": 263}, {"%": 262}], "O": [{"%": 984, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}, {"%": 985, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 986, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 987, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 988, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 989, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 192}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct_2/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 984}, {"%": 259}], "O": [{"%": 990, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct_2/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 990}, {"%": 256}, {"%": 255}, {"%": 258}, {"%": 257}], "O": [{"%": 991, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}, {"%": 992, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 993, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 994, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 995, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 996, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct_2/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 991}], "O": [{"%": 997, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct_3/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 997}, {"%": 254}], "O": [{"%": 998, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct_3/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 998}, {"%": 251}, {"%": 250}, {"%": 253}, {"%": 252}], "O": [{"%": 999, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}, {"%": 1000, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 1001, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 1002, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 1003, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 1004, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 192}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct_3/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 999}, {"%": 249}], "O": [{"%": 1005, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct_3/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 1005}, {"%": 246}, {"%": 245}, {"%": 248}, {"%": 247}], "O": [{"%": 1006, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}, {"%": 1007, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 1008, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 1009, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 1010, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 1011, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct_3/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 1006}], "O": [{"%": 1012, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct_4/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 1012}, {"%": 244}], "O": [{"%": 1013, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct_4/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 1013}, {"%": 241}, {"%": 240}, {"%": 243}, {"%": 242}], "O": [{"%": 1014, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}, {"%": 1015, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 1016, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 1017, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 1018, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 1019, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 192}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct_4/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 1014}, {"%": 239}], "O": [{"%": 1020, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct_4/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 1020}, {"%": 236}, {"%": 235}, {"%": 238}, {"%": 237}], "O": [{"%": 1021, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}, {"%": 1022, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 1023, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 1024, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 1025, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 1026, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct_4/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 1021}], "O": [{"%": 1027, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct_5/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 1027}, {"%": 234}], "O": [{"%": 1028, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct_5/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 1028}, {"%": 231}, {"%": 230}, {"%": 233}, {"%": 232}], "O": [{"%": 1029, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}, {"%": 1030, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 1031, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 1032, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 1033, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 1034, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 192}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct_5/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 1029}, {"%": 229}], "O": [{"%": 1035, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct_5/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 1035}, {"%": 226}, {"%": 225}, {"%": 228}, {"%": 227}], "O": [{"%": 1036, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}, {"%": 1037, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 1038, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 1039, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 1040, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [192], "NCHW", [], 0]}}, {"%": 1041, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/LightConvBNAct_5/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 1036}], "O": [{"%": 1042, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 1.0}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1043, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "0.combine", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/"}, "N": "struct_name"}], "I": [{"%": 952}, {"%": 967}, {"%": 982}, {"%": 997}, {"%": 1012}, {"%": 1027}, {"%": 1042}], "O": [{"%": 1044, "TT": {"#": "0.t_vec", "D": [{"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1024, -1, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 192, -1, -1], "NCHW", [], 0]}]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.concat", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/"}, "N": "struct_name"}], "I": [{"%": 1044}, {"%": 1043}], "O": [{"%": 1045, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 2176, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 1045}, {"%": 224}], "O": [{"%": 1046, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 512, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 1046}, {"%": 221}, {"%": 220}, {"%": 223}, {"%": 222}], "O": [{"%": 1047, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 512, -1, -1], "NCHW", [], 0]}}, {"%": 1048, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, {"%": 1049, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, {"%": 1050, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, {"%": 1051, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [512], "NCHW", [], 0]}}, {"%": 1052, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/ConvBNAct/ReLU/"}, "N": "struct_name"}], "I": [{"%": 1047}], "O": [{"%": 1053, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 512, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 1053}, {"%": 219}], "O": [{"%": 1054, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1024, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 1054}, {"%": 216}, {"%": 215}, {"%": 218}, {"%": 217}], "O": [{"%": 1055, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1024, -1, -1], "NCHW", [], 0]}}, {"%": 1056, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, {"%": 1057, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, {"%": 1058, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, {"%": 1059, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, {"%": 1060, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 1055}], "O": [{"%": 1061, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1024, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_2/Sequential/HGV2_Block_2/"}, "N": "struct_name"}], "I": [{"%": 1061}, {"%": 952}], "O": [{"%": 1062, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1024, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 1024}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 1062}, {"%": 214}], "O": [{"%": 1063, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1024, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 1063}, {"%": 211}, {"%": 210}, {"%": 213}, {"%": 212}], "O": [{"%": 1064, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1024, -1, -1], "NCHW", [], 0]}}, {"%": 1065, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, {"%": 1066, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, {"%": 1067, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, {"%": 1068, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, {"%": 1069, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 1064}, {"%": 209}], "O": [{"%": 1070, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 1070}, {"%": 206}, {"%": 205}, {"%": 208}, {"%": 207}], "O": [{"%": 1071, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}, {"%": 1072, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1073, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1074, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1075, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1076, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 384}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 1071}, {"%": 204}], "O": [{"%": 1077, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 1077}, {"%": 201}, {"%": 200}, {"%": 203}, {"%": 202}], "O": [{"%": 1078, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}, {"%": 1079, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1080, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1081, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1082, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1083, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 1078}], "O": [{"%": 1084, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct_1/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 1084}, {"%": 199}], "O": [{"%": 1085, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct_1/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 1085}, {"%": 196}, {"%": 195}, {"%": 198}, {"%": 197}], "O": [{"%": 1086, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}, {"%": 1087, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1088, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1089, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1090, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1091, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 384}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct_1/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 1086}, {"%": 194}], "O": [{"%": 1092, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct_1/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 1092}, {"%": 191}, {"%": 190}, {"%": 193}, {"%": 192}], "O": [{"%": 1093, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}, {"%": 1094, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1095, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1096, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1097, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1098, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct_1/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 1093}], "O": [{"%": 1099, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct_2/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 1099}, {"%": 189}], "O": [{"%": 1100, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct_2/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 1100}, {"%": 186}, {"%": 185}, {"%": 188}, {"%": 187}], "O": [{"%": 1101, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}, {"%": 1102, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1103, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1104, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1105, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1106, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 384}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct_2/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 1101}, {"%": 184}], "O": [{"%": 1107, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct_2/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 1107}, {"%": 181}, {"%": 180}, {"%": 183}, {"%": 182}], "O": [{"%": 1108, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}, {"%": 1109, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1110, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1111, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1112, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1113, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct_2/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 1108}], "O": [{"%": 1114, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct_3/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 1114}, {"%": 179}], "O": [{"%": 1115, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct_3/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 1115}, {"%": 176}, {"%": 175}, {"%": 178}, {"%": 177}], "O": [{"%": 1116, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}, {"%": 1117, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1118, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1119, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1120, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1121, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 384}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct_3/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 1116}, {"%": 174}], "O": [{"%": 1122, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct_3/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 1122}, {"%": 171}, {"%": 170}, {"%": 173}, {"%": 172}], "O": [{"%": 1123, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}, {"%": 1124, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1125, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1126, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1127, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1128, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct_3/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 1123}], "O": [{"%": 1129, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct_4/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 1129}, {"%": 169}], "O": [{"%": 1130, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct_4/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 1130}, {"%": 166}, {"%": 165}, {"%": 168}, {"%": 167}], "O": [{"%": 1131, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}, {"%": 1132, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1133, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1134, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1135, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1136, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 384}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct_4/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 1131}, {"%": 164}], "O": [{"%": 1137, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct_4/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 1137}, {"%": 161}, {"%": 160}, {"%": 163}, {"%": 162}], "O": [{"%": 1138, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}, {"%": 1139, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1140, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1141, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1142, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1143, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct_4/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 1138}], "O": [{"%": 1144, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct_5/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 1144}, {"%": 159}], "O": [{"%": 1145, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct_5/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 1145}, {"%": 156}, {"%": 155}, {"%": 158}, {"%": 157}], "O": [{"%": 1146, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}, {"%": 1147, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1148, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1149, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1150, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1151, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.depthwise_conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 384}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct_5/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 1146}, {"%": 154}], "O": [{"%": 1152, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct_5/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 1152}, {"%": 151}, {"%": 150}, {"%": 153}, {"%": 152}], "O": [{"%": 1153, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}, {"%": 1154, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1155, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1156, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1157, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [384], "NCHW", [], 0]}}, {"%": 1158, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/LightConvBNAct_5/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 1153}], "O": [{"%": 1159, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 1.0}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1160, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "0.combine", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/"}, "N": "struct_name"}], "I": [{"%": 1064}, {"%": 1084}, {"%": 1099}, {"%": 1114}, {"%": 1129}, {"%": 1144}, {"%": 1159}], "O": [{"%": 1161, "TT": {"#": "0.t_vec", "D": [{"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1024, -1, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 384, -1, -1], "NCHW", [], 0]}]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.concat", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/"}, "N": "struct_name"}], "I": [{"%": 1161}, {"%": 1160}], "O": [{"%": 1162, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 3328, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/ConvBNAct/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 1162}, {"%": 149}], "O": [{"%": 1163, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1024, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/ConvBNAct/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 1163}, {"%": 146}, {"%": 145}, {"%": 148}, {"%": 147}], "O": [{"%": 1164, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1024, -1, -1], "NCHW", [], 0]}}, {"%": 1165, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, {"%": 1166, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, {"%": 1167, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, {"%": 1168, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1024], "NCHW", [], 0]}}, {"%": 1169, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/ConvBNAct/ReLU/"}, "N": "struct_name"}], "I": [{"%": 1164}], "O": [{"%": 1170, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1024, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/ConvBNAct_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 1170}, {"%": 144}], "O": [{"%": 1171, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 2048, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/ConvBNAct_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 1171}, {"%": 141}, {"%": 140}, {"%": 143}, {"%": 142}], "O": [{"%": 1172, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 2048, -1, -1], "NCHW", [], 0]}}, {"%": 1173, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [2048], "NCHW", [], 0]}}, {"%": 1174, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [2048], "NCHW", [], 0]}}, {"%": 1175, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [2048], "NCHW", [], 0]}}, {"%": 1176, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [2048], "NCHW", [], 0]}}, {"%": 1177, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PPHGNetV2/HGV2_Stage_3/Sequential/HGV2_Block/ConvBNAct_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 1172}], "O": [{"%": 1178, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 2048, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 1178}, {"%": 136}], "O": [{"%": 1179, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 256, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 1062}, {"%": 137}], "O": [{"%": 1180, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 256, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/Conv2D_2/"}, "N": "struct_name"}], "I": [{"%": 726}, {"%": 138}], "O": [{"%": 1181, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 256, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/Conv2D_3/"}, "N": "struct_name"}], "I": [{"%": 652}, {"%": 139}], "O": [{"%": 1182, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 256, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.nearest_interp", "A": [{"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_i32", "D": -1}, "N": "out_d"}, {"AT": {"#": "0.a_i32", "D": -1}, "N": "out_h"}, {"AT": {"#": "0.a_i32", "D": -1}, "N": "out_w"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_f32", "D": 2.0}, {"#": "0.a_f32", "D": 2.0}]}, "N": "scale"}, {"AT": {"#": "0.a_str", "D": "nearest"}, "N": "interp_method"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "align_corners"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "align_mode"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/"}, "N": "struct_name"}], "I": [{"%": 1179}, {"%": 0}, {"%": 0}, {"%": 0}], "O": [{"%": 1183, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 256, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/"}, "N": "struct_name"}], "I": [{"%": 1180}, {"%": 1183}], "O": [{"%": 1184, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 256, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.nearest_interp", "A": [{"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_i32", "D": -1}, "N": "out_d"}, {"AT": {"#": "0.a_i32", "D": -1}, "N": "out_h"}, {"AT": {"#": "0.a_i32", "D": -1}, "N": "out_w"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_f32", "D": 2.0}, {"#": "0.a_f32", "D": 2.0}]}, "N": "scale"}, {"AT": {"#": "0.a_str", "D": "nearest"}, "N": "interp_method"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "align_corners"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "align_mode"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/"}, "N": "struct_name"}], "I": [{"%": 1184}, {"%": 0}, {"%": 0}, {"%": 0}], "O": [{"%": 1185, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 256, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/"}, "N": "struct_name"}], "I": [{"%": 1181}, {"%": 1185}], "O": [{"%": 1186, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 256, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.nearest_interp", "A": [{"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_i32", "D": -1}, "N": "out_d"}, {"AT": {"#": "0.a_i32", "D": -1}, "N": "out_h"}, {"AT": {"#": "0.a_i32", "D": -1}, "N": "out_w"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_f32", "D": 2.0}, {"#": "0.a_f32", "D": 2.0}]}, "N": "scale"}, {"AT": {"#": "0.a_str", "D": "nearest"}, "N": "interp_method"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "align_corners"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "align_mode"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/"}, "N": "struct_name"}], "I": [{"%": 1186}, {"%": 0}, {"%": 0}, {"%": 0}], "O": [{"%": 1187, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 256, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/"}, "N": "struct_name"}], "I": [{"%": 1182}, {"%": 1187}], "O": [{"%": 1188, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 256, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 4}, {"#": "0.a_i32", "D": 4}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/Conv2D_4/"}, "N": "struct_name"}], "I": [{"%": 1179}, {"%": 132}], "O": [{"%": 1189, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 4}, {"#": "0.a_i32", "D": 4}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/Conv2D_5/"}, "N": "struct_name"}], "I": [{"%": 1184}, {"%": 133}], "O": [{"%": 1190, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 4}, {"#": "0.a_i32", "D": 4}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/Conv2D_6/"}, "N": "struct_name"}], "I": [{"%": 1186}, {"%": 134}], "O": [{"%": 1191, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 4}, {"#": "0.a_i32", "D": 4}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/Conv2D_7/"}, "N": "struct_name"}], "I": [{"%": 1188}, {"%": 135}], "O": [{"%": 1192, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/Conv2D_8/"}, "N": "struct_name"}], "I": [{"%": 1192}, {"%": 131}], "O": [{"%": 1193, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/"}, "N": "struct_name"}], "I": [{"%": 1191}, {"%": 1193}], "O": [{"%": 1194, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/Conv2D_9/"}, "N": "struct_name"}], "I": [{"%": 1194}, {"%": 130}], "O": [{"%": 1195, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/"}, "N": "struct_name"}], "I": [{"%": 1190}, {"%": 1195}], "O": [{"%": 1196, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/Conv2D_10/"}, "N": "struct_name"}], "I": [{"%": 1196}, {"%": 129}], "O": [{"%": 1197, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/"}, "N": "struct_name"}], "I": [{"%": 1189}, {"%": 1197}], "O": [{"%": 1198, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 4}, {"#": "0.a_i32", "D": 4}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/Conv2D_11/"}, "N": "struct_name"}], "I": [{"%": 1192}, {"%": 128}], "O": [{"%": 1199, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 4}, {"#": "0.a_i32", "D": 4}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/Conv2D_12/"}, "N": "struct_name"}], "I": [{"%": 1194}, {"%": 127}], "O": [{"%": 1200, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 4}, {"#": "0.a_i32", "D": 4}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/Conv2D_13/"}, "N": "struct_name"}], "I": [{"%": 1196}, {"%": 126}], "O": [{"%": 1201, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 4}, {"#": "0.a_i32", "D": 4}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/Conv2D_14/"}, "N": "struct_name"}], "I": [{"%": 1198}, {"%": 125}], "O": [{"%": 1202, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 1202}, {"%": 46}], "O": [{"%": 1203, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1204, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 45}, {"%": 1204}], "O": [{"%": 1205, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 32, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 1203}, {"%": 1205}], "O": [{"%": 1206, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 3}, {"#": "0.a_i32", "D": 3}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 1206}, {"%": 30}], "O": [{"%": 1207, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock/Conv2D_1/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1208, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 29}, {"%": 1208}], "O": [{"%": 1209, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 32, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 1207}, {"%": 1209}], "O": [{"%": 1210, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 3}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock/Conv2D_2/"}, "N": "struct_name"}], "I": [{"%": 1206}, {"%": 42}], "O": [{"%": 1211, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock/Conv2D_2/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1212, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock/Conv2D_2/"}, "N": "struct_name"}], "I": [{"%": 41}, {"%": 1212}], "O": [{"%": 1213, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 32, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock/Conv2D_2/"}, "N": "struct_name"}], "I": [{"%": 1211}, {"%": 1213}], "O": [{"%": 1214, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 3}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock/Conv2D_3/"}, "N": "struct_name"}], "I": [{"%": 1206}, {"%": 36}], "O": [{"%": 1215, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock/Conv2D_3/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1216, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock/Conv2D_3/"}, "N": "struct_name"}], "I": [{"%": 35}, {"%": 1216}], "O": [{"%": 1217, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 32, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock/Conv2D_3/"}, "N": "struct_name"}], "I": [{"%": 1215}, {"%": 1217}], "O": [{"%": 1218, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock/"}, "N": "struct_name"}], "I": [{"%": 1210}, {"%": 1214}], "O": [{"%": 1219, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock/"}, "N": "struct_name"}], "I": [{"%": 1219}, {"%": 1218}], "O": [{"%": 1220, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock/Conv2D_4/"}, "N": "struct_name"}], "I": [{"%": 1220}, {"%": 28}], "O": [{"%": 1221, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock/Conv2D_4/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1222, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock/Conv2D_4/"}, "N": "struct_name"}], "I": [{"%": 27}, {"%": 1222}], "O": [{"%": 1223, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 32, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock/Conv2D_4/"}, "N": "struct_name"}], "I": [{"%": 1221}, {"%": 1223}], "O": [{"%": 1224, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock/Conv2D_5/"}, "N": "struct_name"}], "I": [{"%": 1220}, {"%": 40}], "O": [{"%": 1225, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock/Conv2D_5/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1226, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock/Conv2D_5/"}, "N": "struct_name"}], "I": [{"%": 39}, {"%": 1226}], "O": [{"%": 1227, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 32, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock/Conv2D_5/"}, "N": "struct_name"}], "I": [{"%": 1225}, {"%": 1227}], "O": [{"%": 1228, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock/Conv2D_6/"}, "N": "struct_name"}], "I": [{"%": 1220}, {"%": 34}], "O": [{"%": 1229, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock/Conv2D_6/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1230, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock/Conv2D_6/"}, "N": "struct_name"}], "I": [{"%": 33}, {"%": 1230}], "O": [{"%": 1231, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 32, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock/Conv2D_6/"}, "N": "struct_name"}], "I": [{"%": 1229}, {"%": 1231}], "O": [{"%": 1232, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock/"}, "N": "struct_name"}], "I": [{"%": 1224}, {"%": 1228}], "O": [{"%": 1233, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock/"}, "N": "struct_name"}], "I": [{"%": 1233}, {"%": 1232}], "O": [{"%": 1234, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock/Conv2D_7/"}, "N": "struct_name"}], "I": [{"%": 1234}, {"%": 26}], "O": [{"%": 1235, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock/Conv2D_7/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1236, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock/Conv2D_7/"}, "N": "struct_name"}], "I": [{"%": 25}, {"%": 1236}], "O": [{"%": 1237, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 32, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock/Conv2D_7/"}, "N": "struct_name"}], "I": [{"%": 1235}, {"%": 1237}], "O": [{"%": 1238, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock/Conv2D_8/"}, "N": "struct_name"}], "I": [{"%": 1234}, {"%": 38}], "O": [{"%": 1239, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock/Conv2D_8/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1240, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock/Conv2D_8/"}, "N": "struct_name"}], "I": [{"%": 37}, {"%": 1240}], "O": [{"%": 1241, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 32, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock/Conv2D_8/"}, "N": "struct_name"}], "I": [{"%": 1239}, {"%": 1241}], "O": [{"%": 1242, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock/Conv2D_9/"}, "N": "struct_name"}], "I": [{"%": 1234}, {"%": 32}], "O": [{"%": 1243, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock/Conv2D_9/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1244, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock/Conv2D_9/"}, "N": "struct_name"}], "I": [{"%": 31}, {"%": 1244}], "O": [{"%": 1245, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 32, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock/Conv2D_9/"}, "N": "struct_name"}], "I": [{"%": 1243}, {"%": 1245}], "O": [{"%": 1246, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock/"}, "N": "struct_name"}], "I": [{"%": 1238}, {"%": 1242}], "O": [{"%": 1247, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock/"}, "N": "struct_name"}], "I": [{"%": 1247}, {"%": 1246}], "O": [{"%": 1248, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock/Conv2D_10/"}, "N": "struct_name"}], "I": [{"%": 1248}, {"%": 44}], "O": [{"%": 1249, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock/Conv2D_10/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1250, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock/Conv2D_10/"}, "N": "struct_name"}], "I": [{"%": 43}, {"%": 1250}], "O": [{"%": 1251, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 64, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock/Conv2D_10/"}, "N": "struct_name"}], "I": [{"%": 1249}, {"%": 1251}], "O": [{"%": 1252, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 1252}, {"%": 22}, {"%": 21}, {"%": 24}, {"%": 23}], "O": [{"%": 1253, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}}, {"%": 1254, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 1255, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 1256, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 1257, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 1258, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock/ReLU/"}, "N": "struct_name"}], "I": [{"%": 1253}], "O": [{"%": 1259, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock/"}, "N": "struct_name"}], "I": [{"%": 1202}, {"%": 1259}], "O": [{"%": 1260, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 1201}, {"%": 72}], "O": [{"%": 1261, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_1/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1262, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 71}, {"%": 1262}], "O": [{"%": 1263, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 32, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_1/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 1261}, {"%": 1263}], "O": [{"%": 1264, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 3}, {"#": "0.a_i32", "D": 3}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_1/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 1264}, {"%": 56}], "O": [{"%": 1265, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_1/Conv2D_1/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1266, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_1/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 55}, {"%": 1266}], "O": [{"%": 1267, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 32, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_1/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 1265}, {"%": 1267}], "O": [{"%": 1268, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 3}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_1/Conv2D_2/"}, "N": "struct_name"}], "I": [{"%": 1264}, {"%": 68}], "O": [{"%": 1269, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_1/Conv2D_2/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1270, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_1/Conv2D_2/"}, "N": "struct_name"}], "I": [{"%": 67}, {"%": 1270}], "O": [{"%": 1271, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 32, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_1/Conv2D_2/"}, "N": "struct_name"}], "I": [{"%": 1269}, {"%": 1271}], "O": [{"%": 1272, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 3}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_1/Conv2D_3/"}, "N": "struct_name"}], "I": [{"%": 1264}, {"%": 62}], "O": [{"%": 1273, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_1/Conv2D_3/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1274, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_1/Conv2D_3/"}, "N": "struct_name"}], "I": [{"%": 61}, {"%": 1274}], "O": [{"%": 1275, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 32, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_1/Conv2D_3/"}, "N": "struct_name"}], "I": [{"%": 1273}, {"%": 1275}], "O": [{"%": 1276, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_1/"}, "N": "struct_name"}], "I": [{"%": 1268}, {"%": 1272}], "O": [{"%": 1277, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_1/"}, "N": "struct_name"}], "I": [{"%": 1277}, {"%": 1276}], "O": [{"%": 1278, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_1/Conv2D_4/"}, "N": "struct_name"}], "I": [{"%": 1278}, {"%": 54}], "O": [{"%": 1279, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_1/Conv2D_4/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1280, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_1/Conv2D_4/"}, "N": "struct_name"}], "I": [{"%": 53}, {"%": 1280}], "O": [{"%": 1281, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 32, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_1/Conv2D_4/"}, "N": "struct_name"}], "I": [{"%": 1279}, {"%": 1281}], "O": [{"%": 1282, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_1/Conv2D_5/"}, "N": "struct_name"}], "I": [{"%": 1278}, {"%": 66}], "O": [{"%": 1283, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_1/Conv2D_5/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1284, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_1/Conv2D_5/"}, "N": "struct_name"}], "I": [{"%": 65}, {"%": 1284}], "O": [{"%": 1285, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 32, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_1/Conv2D_5/"}, "N": "struct_name"}], "I": [{"%": 1283}, {"%": 1285}], "O": [{"%": 1286, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_1/Conv2D_6/"}, "N": "struct_name"}], "I": [{"%": 1278}, {"%": 60}], "O": [{"%": 1287, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_1/Conv2D_6/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1288, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_1/Conv2D_6/"}, "N": "struct_name"}], "I": [{"%": 59}, {"%": 1288}], "O": [{"%": 1289, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 32, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_1/Conv2D_6/"}, "N": "struct_name"}], "I": [{"%": 1287}, {"%": 1289}], "O": [{"%": 1290, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_1/"}, "N": "struct_name"}], "I": [{"%": 1282}, {"%": 1286}], "O": [{"%": 1291, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_1/"}, "N": "struct_name"}], "I": [{"%": 1291}, {"%": 1290}], "O": [{"%": 1292, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_1/Conv2D_7/"}, "N": "struct_name"}], "I": [{"%": 1292}, {"%": 52}], "O": [{"%": 1293, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_1/Conv2D_7/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1294, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_1/Conv2D_7/"}, "N": "struct_name"}], "I": [{"%": 51}, {"%": 1294}], "O": [{"%": 1295, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 32, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_1/Conv2D_7/"}, "N": "struct_name"}], "I": [{"%": 1293}, {"%": 1295}], "O": [{"%": 1296, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_1/Conv2D_8/"}, "N": "struct_name"}], "I": [{"%": 1292}, {"%": 64}], "O": [{"%": 1297, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_1/Conv2D_8/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1298, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_1/Conv2D_8/"}, "N": "struct_name"}], "I": [{"%": 63}, {"%": 1298}], "O": [{"%": 1299, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 32, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_1/Conv2D_8/"}, "N": "struct_name"}], "I": [{"%": 1297}, {"%": 1299}], "O": [{"%": 1300, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_1/Conv2D_9/"}, "N": "struct_name"}], "I": [{"%": 1292}, {"%": 58}], "O": [{"%": 1301, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_1/Conv2D_9/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1302, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_1/Conv2D_9/"}, "N": "struct_name"}], "I": [{"%": 57}, {"%": 1302}], "O": [{"%": 1303, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 32, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_1/Conv2D_9/"}, "N": "struct_name"}], "I": [{"%": 1301}, {"%": 1303}], "O": [{"%": 1304, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_1/"}, "N": "struct_name"}], "I": [{"%": 1296}, {"%": 1300}], "O": [{"%": 1305, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_1/"}, "N": "struct_name"}], "I": [{"%": 1305}, {"%": 1304}], "O": [{"%": 1306, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_1/Conv2D_10/"}, "N": "struct_name"}], "I": [{"%": 1306}, {"%": 70}], "O": [{"%": 1307, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_1/Conv2D_10/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1308, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_1/Conv2D_10/"}, "N": "struct_name"}], "I": [{"%": 69}, {"%": 1308}], "O": [{"%": 1309, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 64, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_1/Conv2D_10/"}, "N": "struct_name"}], "I": [{"%": 1307}, {"%": 1309}], "O": [{"%": 1310, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_1/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 1310}, {"%": 48}, {"%": 47}, {"%": 50}, {"%": 49}], "O": [{"%": 1311, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}}, {"%": 1312, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 1313, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 1314, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 1315, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 1316, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_1/ReLU/"}, "N": "struct_name"}], "I": [{"%": 1311}], "O": [{"%": 1317, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_1/"}, "N": "struct_name"}], "I": [{"%": 1201}, {"%": 1317}], "O": [{"%": 1318, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_2/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 1200}, {"%": 98}], "O": [{"%": 1319, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_2/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1320, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_2/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 97}, {"%": 1320}], "O": [{"%": 1321, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 32, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_2/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 1319}, {"%": 1321}], "O": [{"%": 1322, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 3}, {"#": "0.a_i32", "D": 3}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_2/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 1322}, {"%": 82}], "O": [{"%": 1323, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_2/Conv2D_1/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1324, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_2/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 81}, {"%": 1324}], "O": [{"%": 1325, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 32, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_2/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 1323}, {"%": 1325}], "O": [{"%": 1326, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 3}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_2/Conv2D_2/"}, "N": "struct_name"}], "I": [{"%": 1322}, {"%": 94}], "O": [{"%": 1327, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_2/Conv2D_2/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1328, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_2/Conv2D_2/"}, "N": "struct_name"}], "I": [{"%": 93}, {"%": 1328}], "O": [{"%": 1329, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 32, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_2/Conv2D_2/"}, "N": "struct_name"}], "I": [{"%": 1327}, {"%": 1329}], "O": [{"%": 1330, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 3}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_2/Conv2D_3/"}, "N": "struct_name"}], "I": [{"%": 1322}, {"%": 88}], "O": [{"%": 1331, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_2/Conv2D_3/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1332, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_2/Conv2D_3/"}, "N": "struct_name"}], "I": [{"%": 87}, {"%": 1332}], "O": [{"%": 1333, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 32, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_2/Conv2D_3/"}, "N": "struct_name"}], "I": [{"%": 1331}, {"%": 1333}], "O": [{"%": 1334, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_2/"}, "N": "struct_name"}], "I": [{"%": 1326}, {"%": 1330}], "O": [{"%": 1335, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_2/"}, "N": "struct_name"}], "I": [{"%": 1335}, {"%": 1334}], "O": [{"%": 1336, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_2/Conv2D_4/"}, "N": "struct_name"}], "I": [{"%": 1336}, {"%": 80}], "O": [{"%": 1337, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_2/Conv2D_4/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1338, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_2/Conv2D_4/"}, "N": "struct_name"}], "I": [{"%": 79}, {"%": 1338}], "O": [{"%": 1339, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 32, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_2/Conv2D_4/"}, "N": "struct_name"}], "I": [{"%": 1337}, {"%": 1339}], "O": [{"%": 1340, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_2/Conv2D_5/"}, "N": "struct_name"}], "I": [{"%": 1336}, {"%": 92}], "O": [{"%": 1341, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_2/Conv2D_5/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1342, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_2/Conv2D_5/"}, "N": "struct_name"}], "I": [{"%": 91}, {"%": 1342}], "O": [{"%": 1343, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 32, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_2/Conv2D_5/"}, "N": "struct_name"}], "I": [{"%": 1341}, {"%": 1343}], "O": [{"%": 1344, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_2/Conv2D_6/"}, "N": "struct_name"}], "I": [{"%": 1336}, {"%": 86}], "O": [{"%": 1345, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_2/Conv2D_6/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1346, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_2/Conv2D_6/"}, "N": "struct_name"}], "I": [{"%": 85}, {"%": 1346}], "O": [{"%": 1347, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 32, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_2/Conv2D_6/"}, "N": "struct_name"}], "I": [{"%": 1345}, {"%": 1347}], "O": [{"%": 1348, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_2/"}, "N": "struct_name"}], "I": [{"%": 1340}, {"%": 1344}], "O": [{"%": 1349, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_2/"}, "N": "struct_name"}], "I": [{"%": 1349}, {"%": 1348}], "O": [{"%": 1350, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_2/Conv2D_7/"}, "N": "struct_name"}], "I": [{"%": 1350}, {"%": 78}], "O": [{"%": 1351, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_2/Conv2D_7/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1352, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_2/Conv2D_7/"}, "N": "struct_name"}], "I": [{"%": 77}, {"%": 1352}], "O": [{"%": 1353, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 32, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_2/Conv2D_7/"}, "N": "struct_name"}], "I": [{"%": 1351}, {"%": 1353}], "O": [{"%": 1354, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_2/Conv2D_8/"}, "N": "struct_name"}], "I": [{"%": 1350}, {"%": 90}], "O": [{"%": 1355, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_2/Conv2D_8/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1356, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_2/Conv2D_8/"}, "N": "struct_name"}], "I": [{"%": 89}, {"%": 1356}], "O": [{"%": 1357, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 32, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_2/Conv2D_8/"}, "N": "struct_name"}], "I": [{"%": 1355}, {"%": 1357}], "O": [{"%": 1358, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_2/Conv2D_9/"}, "N": "struct_name"}], "I": [{"%": 1350}, {"%": 84}], "O": [{"%": 1359, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_2/Conv2D_9/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1360, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_2/Conv2D_9/"}, "N": "struct_name"}], "I": [{"%": 83}, {"%": 1360}], "O": [{"%": 1361, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 32, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_2/Conv2D_9/"}, "N": "struct_name"}], "I": [{"%": 1359}, {"%": 1361}], "O": [{"%": 1362, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_2/"}, "N": "struct_name"}], "I": [{"%": 1354}, {"%": 1358}], "O": [{"%": 1363, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_2/"}, "N": "struct_name"}], "I": [{"%": 1363}, {"%": 1362}], "O": [{"%": 1364, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_2/Conv2D_10/"}, "N": "struct_name"}], "I": [{"%": 1364}, {"%": 96}], "O": [{"%": 1365, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_2/Conv2D_10/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1366, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_2/Conv2D_10/"}, "N": "struct_name"}], "I": [{"%": 95}, {"%": 1366}], "O": [{"%": 1367, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 64, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_2/Conv2D_10/"}, "N": "struct_name"}], "I": [{"%": 1365}, {"%": 1367}], "O": [{"%": 1368, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_2/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 1368}, {"%": 74}, {"%": 73}, {"%": 76}, {"%": 75}], "O": [{"%": 1369, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}}, {"%": 1370, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 1371, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 1372, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 1373, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 1374, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_2/ReLU/"}, "N": "struct_name"}], "I": [{"%": 1369}], "O": [{"%": 1375, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_2/"}, "N": "struct_name"}], "I": [{"%": 1200}, {"%": 1375}], "O": [{"%": 1376, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_3/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 1199}, {"%": 124}], "O": [{"%": 1377, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_3/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1378, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_3/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 123}, {"%": 1378}], "O": [{"%": 1379, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 32, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_3/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 1377}, {"%": 1379}], "O": [{"%": 1380, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 3}, {"#": "0.a_i32", "D": 3}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_3/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 1380}, {"%": 108}], "O": [{"%": 1381, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_3/Conv2D_1/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1382, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_3/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 107}, {"%": 1382}], "O": [{"%": 1383, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 32, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_3/Conv2D_1/"}, "N": "struct_name"}], "I": [{"%": 1381}, {"%": 1383}], "O": [{"%": 1384, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 3}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_3/Conv2D_2/"}, "N": "struct_name"}], "I": [{"%": 1380}, {"%": 120}], "O": [{"%": 1385, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_3/Conv2D_2/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1386, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_3/Conv2D_2/"}, "N": "struct_name"}], "I": [{"%": 119}, {"%": 1386}], "O": [{"%": 1387, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 32, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_3/Conv2D_2/"}, "N": "struct_name"}], "I": [{"%": 1385}, {"%": 1387}], "O": [{"%": 1388, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 3}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_3/Conv2D_3/"}, "N": "struct_name"}], "I": [{"%": 1380}, {"%": 114}], "O": [{"%": 1389, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_3/Conv2D_3/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1390, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_3/Conv2D_3/"}, "N": "struct_name"}], "I": [{"%": 113}, {"%": 1390}], "O": [{"%": 1391, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 32, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_3/Conv2D_3/"}, "N": "struct_name"}], "I": [{"%": 1389}, {"%": 1391}], "O": [{"%": 1392, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_3/"}, "N": "struct_name"}], "I": [{"%": 1384}, {"%": 1388}], "O": [{"%": 1393, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_3/"}, "N": "struct_name"}], "I": [{"%": 1393}, {"%": 1392}], "O": [{"%": 1394, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_3/Conv2D_4/"}, "N": "struct_name"}], "I": [{"%": 1394}, {"%": 106}], "O": [{"%": 1395, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_3/Conv2D_4/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1396, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_3/Conv2D_4/"}, "N": "struct_name"}], "I": [{"%": 105}, {"%": 1396}], "O": [{"%": 1397, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 32, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_3/Conv2D_4/"}, "N": "struct_name"}], "I": [{"%": 1395}, {"%": 1397}], "O": [{"%": 1398, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_3/Conv2D_5/"}, "N": "struct_name"}], "I": [{"%": 1394}, {"%": 118}], "O": [{"%": 1399, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_3/Conv2D_5/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1400, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_3/Conv2D_5/"}, "N": "struct_name"}], "I": [{"%": 117}, {"%": 1400}], "O": [{"%": 1401, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 32, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_3/Conv2D_5/"}, "N": "struct_name"}], "I": [{"%": 1399}, {"%": 1401}], "O": [{"%": 1402, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 2}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_3/Conv2D_6/"}, "N": "struct_name"}], "I": [{"%": 1394}, {"%": 112}], "O": [{"%": 1403, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_3/Conv2D_6/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1404, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_3/Conv2D_6/"}, "N": "struct_name"}], "I": [{"%": 111}, {"%": 1404}], "O": [{"%": 1405, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 32, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_3/Conv2D_6/"}, "N": "struct_name"}], "I": [{"%": 1403}, {"%": 1405}], "O": [{"%": 1406, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_3/"}, "N": "struct_name"}], "I": [{"%": 1398}, {"%": 1402}], "O": [{"%": 1407, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_3/"}, "N": "struct_name"}], "I": [{"%": 1407}, {"%": 1406}], "O": [{"%": 1408, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_3/Conv2D_7/"}, "N": "struct_name"}], "I": [{"%": 1408}, {"%": 104}], "O": [{"%": 1409, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_3/Conv2D_7/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1410, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_3/Conv2D_7/"}, "N": "struct_name"}], "I": [{"%": 103}, {"%": 1410}], "O": [{"%": 1411, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 32, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_3/Conv2D_7/"}, "N": "struct_name"}], "I": [{"%": 1409}, {"%": 1411}], "O": [{"%": 1412, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_3/Conv2D_8/"}, "N": "struct_name"}], "I": [{"%": 1408}, {"%": 116}], "O": [{"%": 1413, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_3/Conv2D_8/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1414, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_3/Conv2D_8/"}, "N": "struct_name"}], "I": [{"%": 115}, {"%": 1414}], "O": [{"%": 1415, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 32, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_3/Conv2D_8/"}, "N": "struct_name"}], "I": [{"%": 1413}, {"%": 1415}], "O": [{"%": 1416, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_3/Conv2D_9/"}, "N": "struct_name"}], "I": [{"%": 1408}, {"%": 110}], "O": [{"%": 1417, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_3/Conv2D_9/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1418, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_3/Conv2D_9/"}, "N": "struct_name"}], "I": [{"%": 109}, {"%": 1418}], "O": [{"%": 1419, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 32, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_3/Conv2D_9/"}, "N": "struct_name"}], "I": [{"%": 1417}, {"%": 1419}], "O": [{"%": 1420, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_3/"}, "N": "struct_name"}], "I": [{"%": 1412}, {"%": 1416}], "O": [{"%": 1421, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_3/"}, "N": "struct_name"}], "I": [{"%": 1421}, {"%": 1420}], "O": [{"%": 1422, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 32, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_3/Conv2D_10/"}, "N": "struct_name"}], "I": [{"%": 1422}, {"%": 122}], "O": [{"%": 1423, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_3/Conv2D_10/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1424, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_3/Conv2D_10/"}, "N": "struct_name"}], "I": [{"%": 121}, {"%": 1424}], "O": [{"%": 1425, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 64, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_3/Conv2D_10/"}, "N": "struct_name"}], "I": [{"%": 1423}, {"%": 1425}], "O": [{"%": 1426, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_3/BatchNorm2D/"}, "N": "struct_name"}], "I": [{"%": 1426}, {"%": 100}, {"%": 99}, {"%": 102}, {"%": 101}], "O": [{"%": 1427, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}}, {"%": 1428, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 1429, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 1430, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 1431, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 1432, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_3/ReLU/"}, "N": "struct_name"}], "I": [{"%": 1427}], "O": [{"%": 1433, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/IntraCLBlock_3/"}, "N": "struct_name"}], "I": [{"%": 1199}, {"%": 1433}], "O": [{"%": 1434, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.nearest_interp", "A": [{"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_i32", "D": -1}, "N": "out_d"}, {"AT": {"#": "0.a_i32", "D": -1}, "N": "out_h"}, {"AT": {"#": "0.a_i32", "D": -1}, "N": "out_w"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_f32", "D": 8.0}, {"#": "0.a_f32", "D": 8.0}]}, "N": "scale"}, {"AT": {"#": "0.a_str", "D": "nearest"}, "N": "interp_method"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "align_corners"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "align_mode"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/"}, "N": "struct_name"}], "I": [{"%": 1260}, {"%": 0}, {"%": 0}, {"%": 0}], "O": [{"%": 1435, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.nearest_interp", "A": [{"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_i32", "D": -1}, "N": "out_d"}, {"AT": {"#": "0.a_i32", "D": -1}, "N": "out_h"}, {"AT": {"#": "0.a_i32", "D": -1}, "N": "out_w"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_f32", "D": 4.0}, {"#": "0.a_f32", "D": 4.0}]}, "N": "scale"}, {"AT": {"#": "0.a_str", "D": "nearest"}, "N": "interp_method"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "align_corners"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "align_mode"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/"}, "N": "struct_name"}], "I": [{"%": 1318}, {"%": 0}, {"%": 0}, {"%": 0}], "O": [{"%": 1436, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.nearest_interp", "A": [{"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_i32", "D": -1}, "N": "out_d"}, {"AT": {"#": "0.a_i32", "D": -1}, "N": "out_h"}, {"AT": {"#": "0.a_i32", "D": -1}, "N": "out_w"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_f32", "D": 2.0}, {"#": "0.a_f32", "D": 2.0}]}, "N": "scale"}, {"AT": {"#": "0.a_str", "D": "nearest"}, "N": "interp_method"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "align_corners"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "align_mode"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/"}, "N": "struct_name"}], "I": [{"%": 1376}, {"%": 0}, {"%": 0}, {"%": 0}], "O": [{"%": 1437, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 1.0}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/LKPAN/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1438, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "0.combine", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/"}, "N": "struct_name"}], "I": [{"%": 1435}, {"%": 1436}, {"%": 1437}, {"%": 1434}], "O": [{"%": 1439, "TT": {"#": "0.t_vec", "D": [{"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.concat", "A": [{"AT": {"#": "0.a_str", "D": "/LKPAN/"}, "N": "struct_name"}], "I": [{"%": 1439}, {"%": 1438}], "O": [{"%": 1440, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 256, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PFHeadLocal/Head/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 1440}, {"%": 20}], "O": [{"%": 1441, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PFHeadLocal/Head/BatchNorm/"}, "N": "struct_name"}], "I": [{"%": 1441}, {"%": 17}, {"%": 16}, {"%": 19}, {"%": 18}], "O": [{"%": 1442, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}}, {"%": 1443, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 1444, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 1445, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 1446, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 1447, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PFHeadLocal/Head/BatchNorm/"}, "N": "struct_name"}], "I": [{"%": 1442}], "O": [{"%": 1448, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": []}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PFHeadLocal/Head/Conv2DTranspose/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1449, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [0], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d_transpose", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_array", "D": []}, "N": "output_padding"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PFHeadLocal/Head/Conv2DTranspose/"}, "N": "struct_name"}], "I": [{"%": 1448}, {"%": 15}, {"%": 1449}], "O": [{"%": 1450, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 64}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PFHeadLocal/Head/Conv2DTranspose/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1451, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PFHeadLocal/Head/Conv2DTranspose/"}, "N": "struct_name"}], "I": [{"%": 14}, {"%": 1451}], "O": [{"%": 1452, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 64, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PFHeadLocal/Head/Conv2DTranspose/"}, "N": "struct_name"}], "I": [{"%": 1450}, {"%": 1452}], "O": [{"%": 1453, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PFHeadLocal/Head/BatchNorm_1/"}, "N": "struct_name"}], "I": [{"%": 1453}, {"%": 11}, {"%": 10}, {"%": 13}, {"%": 12}], "O": [{"%": 1454, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}}, {"%": 1455, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 1456, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 1457, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 1458, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 1459, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PFHeadLocal/Head/BatchNorm_1/"}, "N": "struct_name"}], "I": [{"%": 1454}], "O": [{"%": 1460, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": []}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PFHeadLocal/Head/Conv2DTranspose_1/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1461, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [0], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d_transpose", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 2}, {"#": "0.a_i32", "D": 2}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_array", "D": []}, "N": "output_padding"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PFHeadLocal/Head/Conv2DTranspose_1/"}, "N": "struct_name"}], "I": [{"%": 1460}, {"%": 9}, {"%": 1461}], "O": [{"%": 1462, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PFHeadLocal/Head/Conv2DTranspose_1/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1463, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PFHeadLocal/Head/Conv2DTranspose_1/"}, "N": "struct_name"}], "I": [{"%": 8}, {"%": 1463}], "O": [{"%": 1464, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 1, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PFHeadLocal/Head/Conv2DTranspose_1/"}, "N": "struct_name"}], "I": [{"%": 1462}, {"%": 1464}], "O": [{"%": 1465, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.sig<PERSON><PERSON>", "A": [{"AT": {"#": "0.a_str", "D": "/PFHeadLocal/Head/"}, "N": "struct_name"}], "I": [{"%": 1465}], "O": [{"%": 1466, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.nearest_interp", "A": [{"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_i32", "D": -1}, "N": "out_d"}, {"AT": {"#": "0.a_i32", "D": -1}, "N": "out_h"}, {"AT": {"#": "0.a_i32", "D": -1}, "N": "out_w"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_f32", "D": 2.0}, {"#": "0.a_f32", "D": 2.0}]}, "N": "scale"}, {"AT": {"#": "0.a_str", "D": "nearest"}, "N": "interp_method"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "align_corners"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "align_mode"}, {"AT": {"#": "0.a_str", "D": "/PFHeadLocal/Upsample/"}, "N": "struct_name"}], "I": [{"%": 1460}, {"%": 0}, {"%": 0}, {"%": 0}], "O": [{"%": 1467, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 1.0}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PFHeadLocal/LocalModule/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1468, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "0.combine", "A": [{"AT": {"#": "0.a_str", "D": "/PFHeadLocal/LocalModule/"}, "N": "struct_name"}], "I": [{"%": 1466}, {"%": 1467}], "O": [{"%": 1469, "TT": {"#": "0.t_vec", "D": [{"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1, -1, -1], "NCHW", [], 0]}, {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.concat", "A": [{"AT": {"#": "0.a_str", "D": "/PFHeadLocal/LocalModule/"}, "N": "struct_name"}], "I": [{"%": 1469}, {"%": 1468}], "O": [{"%": 1470, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 65, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PFHeadLocal/LocalModule/ConvBNLayer/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 1470}, {"%": 7}], "O": [{"%": 1471, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.batch_norm_", "A": [{"AT": {"#": "0.a_bool", "D": true}, "N": "is_test"}, {"AT": {"#": "0.a_f32", "D": 0.8999999761581421}, "N": "momentum"}, {"AT": {"#": "0.a_f32", "D": 9.999999747378752e-06}, "N": "epsilon"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "use_global_stats"}, {"AT": {"#": "0.a_bool", "D": false}, "N": "trainable_statistics"}, {"AT": {"#": "0.a_str", "D": "/PFHeadLocal/LocalModule/ConvBNLayer/BatchNorm/"}, "N": "struct_name"}], "I": [{"%": 1471}, {"%": 4}, {"%": 3}, {"%": 6}, {"%": 5}], "O": [{"%": 1472, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}}, {"%": 1473, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 1474, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 1475, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 1476, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [64], "NCHW", [], 0]}}, {"%": 1477, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_ui8"}, [-1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}, {"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.relu", "A": [{"AT": {"#": "0.a_str", "D": "/PFHeadLocal/LocalModule/ConvBNLayer/"}, "N": "struct_name"}], "I": [{"%": 1472}], "O": [{"%": 1478, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 64, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.conv2d", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "strides"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 0}, {"#": "0.a_i32", "D": 0}]}, "N": "paddings"}, {"AT": {"#": "0.a_str", "D": "EXPLICIT"}, "N": "padding_algorithm"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_i32", "D": 1}, {"#": "0.a_i32", "D": 1}]}, "N": "dilations"}, {"AT": {"#": "0.a_i32", "D": 1}, "N": "groups"}, {"AT": {"#": "0.a_str", "D": "NCHW"}, "N": "data_format"}, {"AT": {"#": "0.a_str", "D": "/PFHeadLocal/LocalModule/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 1478}, {"%": 2}], "O": [{"%": 1479, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full_int_array", "A": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": -1}, {"#": "0.a_i64", "D": 1}, {"#": "0.a_i64", "D": 1}]}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "int64"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PFHeadLocal/LocalModule/Conv2D/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1480, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_i64"}, [4], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.reshape", "A": [{"AT": {"#": "0.a_str", "D": "/PFHeadLocal/LocalModule/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 1}, {"%": 1480}], "O": [{"%": 1481, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1, 1, 1, 1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PFHeadLocal/LocalModule/Conv2D/"}, "N": "struct_name"}], "I": [{"%": 1479}, {"%": 1481}], "O": [{"%": 1482, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.sig<PERSON><PERSON>", "A": [{"AT": {"#": "0.a_str", "D": "/PFHeadLocal/"}, "N": "struct_name"}], "I": [{"%": 1482}], "O": [{"%": 1483, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.add", "A": [{"AT": {"#": "0.a_str", "D": "/PFHeadLocal/"}, "N": "struct_name"}], "I": [{"%": 1466}, {"%": 1483}], "O": [{"%": 1484, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.full", "A": [{"AT": {"#": "1.a_intarray", "D": [1]}, "N": "shape"}, {"AT": {"#": "0.a_f64", "D": 0.5}, "N": "value"}, {"AT": {"#": "1.a_dtype", "D": "float32"}, "N": "dtype"}, {"AT": {"#": "1.a_place", "D": [1, 0, ""]}, "N": "place"}, {"AT": {"#": "0.a_str", "D": "/PFHeadLocal/"}, "N": "struct_name"}], "I": [], "O": [{"%": 1485, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "stop_gradient"}]}, {"#": "1.scale", "A": [{"AT": {"#": "0.a_f32", "D": 0.0}, "N": "bias"}, {"AT": {"#": "0.a_bool", "D": true}, "N": "bias_after_scale"}, {"AT": {"#": "0.a_str", "D": "/PFHeadLocal/"}, "N": "struct_name"}], "I": [{"%": 1484}, {"%": 1485}], "O": [{"%": 1486, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}, {"#": "1.fetch", "A": [{"AT": {"#": "0.a_str", "D": "fetch_name_0"}, "N": "name"}, {"AT": {"#": "0.a_i32", "D": 0}, "N": "col"}], "I": [{"%": 1486}], "O": [{"%": 1487, "TT": {"#": "0.t_dtensor", "D": [{"#": "0.t_f32"}, [-1, 1, -1, -1], "NCHW", [], 0]}}], "OA": [{"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": true}]}, "N": "persistable"}, {"AT": {"#": "0.a_array", "D": [{"#": "0.a_bool", "D": false}]}, "N": "stop_gradient"}]}]}]}]}}