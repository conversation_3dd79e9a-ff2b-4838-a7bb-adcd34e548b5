import os
import tempfile
import logging
from typing import List
import shutil

logger = logging.getLogger(__name__)

# 支持的文件类型
SUPPORTED_IMAGE_TYPES = {'.jpg', '.jpeg', '.png', '.bmp'}
SUPPORTED_PDF_TYPES = {'.pdf'}
SUPPORTED_TYPES = SUPPORTED_IMAGE_TYPES | SUPPORTED_PDF_TYPES

async def save_upload_file(upload_file) -> str:
    """
    保存上传的文件到临时目录
    
    Args:
        upload_file: 上传的文件对象
        
    Returns:
        保存的文件路径
    """
    try:
        # 创建临时文件
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(upload_file.filename)[1])
        temp_file.close()
        
        # 保存文件内容
        with open(temp_file.name, "wb") as buffer:
            shutil.copyfileobj(upload_file.file, buffer)
        
        logger.info(f"文件已保存到: {temp_file.name}")
        return temp_file.name
        
    except Exception as e:
        logger.error(f"保存文件失败: {e}")
        raise

def is_valid_file_type(filename: str) -> bool:
    """
    检查文件类型是否支持
    
    Args:
        filename: 文件名
        
    Returns:
        是否支持该文件类型
    """
    if not filename:
        return False
    
    file_ext = os.path.splitext(filename.lower())[1]
    return file_ext in SUPPORTED_TYPES



def cleanup_temp_files(file_paths: List[str]):
    """
    清理临时文件
    
    Args:
        file_paths: 要清理的文件路径列表
    """
    for file_path in file_paths:
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.debug(f"已清理临时文件: {file_path}")
        except Exception as e:
            logger.warning(f"清理临时文件失败 {file_path}: {e}")

def get_file_size_mb(file_path: str) -> float:
    """
    获取文件大小（MB）
    
    Args:
        file_path: 文件路径
        
    Returns:
        文件大小（MB）
    """
    try:
        size_bytes = os.path.getsize(file_path)
        return size_bytes / (1024 * 1024)
    except Exception as e:
        logger.error(f"获取文件大小失败: {e}")
        return 0.0

def validate_file_size(file_path: str, max_size_mb: float = 50.0) -> bool:
    """
    验证文件大小是否在限制范围内
    
    Args:
        file_path: 文件路径
        max_size_mb: 最大文件大小（MB）
        
    Returns:
        文件大小是否合法
    """
    file_size = get_file_size_mb(file_path)
    return file_size <= max_size_mb 