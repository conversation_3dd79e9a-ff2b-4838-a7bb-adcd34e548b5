import asyncio
import logging
from typing import Dict, List, Any, Optional
import os
import tempfile
import concurrent.futures
import signal
from paddleocr import PaddleOCR
import numpy as np
import cv2

logger = logging.getLogger(__name__)

class OCRService:
    """OCR服务类"""
    
    def __init__(self):
        self.ocr = None
        self.initialized = False
        # 检查是否强制使用CPU模式
        force_cpu = os.getenv('FORCE_CPU_MODE', '0').lower() in ('1', 'true', 'yes')
        ocr_device = os.getenv('OCR_DEVICE', 'npu:0')  # 使用NPU 0（逻辑ID）
        self.device = "cpu" if force_cpu else ocr_device
        logger.info(f"OCR设备设置: {self.device} (强制CPU模式: {force_cpu})")
        
    async def initialize(self):
        """初始化OCR服务"""
        try:
            logger.info("正在初始化PaddleOCR...")
            os.environ['LD_LIBRARY_PATH'] = '/usr/local/Ascend/lib64:' + os.getenv('LD_LIBRARY_PATH', '')
            logger.info(f"LD_LIBRARY_PATH: {os.environ['LD_LIBRARY_PATH']}")
            logger.info(os.access("/usr/local/lib/python3.10/dist-packages/paddle_custom_device/libpaddle-custom-npu.so",os.R_OK))

            # 使用超时机制在异步环境中同步初始化PaddleOCR
            loop = asyncio.get_event_loop()

            # 设置初始化超时时间为5分钟
            timeout = 300  # 5分钟
            try:
                await asyncio.wait_for(
                    loop.run_in_executor(None, self._init_ocr),
                    timeout=timeout
                )
            except asyncio.TimeoutError:
                logger.error(f"PaddleOCR初始化超时（{timeout}秒），尝试强制使用CPU模式")
                # 超时后强制使用CPU模式
                await asyncio.wait_for(
                    loop.run_in_executor(None, self._init_ocr_cpu_only),
                    timeout=120  # CPU模式2分钟超时
                )

            self.initialized = True
            logger.info("PaddleOCR初始化完成")

        except Exception as e:
            logger.error(f"PaddleOCR初始化失败: {e}")
            raise
    
    def _init_ocr(self):
        """同步初始化PaddleOCR（根据设备配置选择模式）"""
        # 指定本地模型路径
        det_model_dir = "/root/.paddlex/official_models/PP-OCRv5_server_det"
        rec_model_dir = "/root/.paddlex/official_models/PP-OCRv5_server_rec"

        # 如果设备已经是CPU，直接使用CPU模式
        if self.device == "cpu":
            logger.info("直接使用CPU模式初始化PaddleOCR...")
            try:
                self.ocr = PaddleOCR(
                    use_textline_orientation=True,
                    lang='ch',
                    device='cpu',
                    det_model_dir=det_model_dir,
                    rec_model_dir=rec_model_dir
                )
                logger.info("PaddleOCR CPU模式初始化成功")
                return
            except Exception as e:
                logger.error(f"CPU模式初始化失败: {e}")
                raise RuntimeError(f"CPU模式初始化失败: {e}")

        # 尝试NPU初始化
        try:
            logger.info(f"尝试使用设备 {self.device} 初始化PaddleOCR...")
            self.ocr = PaddleOCR(
                use_textline_orientation=True,  # 使用文本行方向分类器
                lang='ch',  # 中文模型
                device=self.device,  # 使用指定设备
                det_model_dir=det_model_dir,  # 检测模型路径
                rec_model_dir=rec_model_dir   # 识别模型路径
            )
            logger.info(f"PaddleOCR {self.device} 模式初始化成功")
            return

        except Exception as e:
            logger.warning(f"{self.device} 初始化失败: {e}")
            logger.info("设备初始化失败，尝试使用CPU...")

        # 设备失败后尝试CPU
        try:
            self.ocr = PaddleOCR(
                use_textline_orientation=True,
                lang='ch',
                device='cpu',
                det_model_dir=det_model_dir,
                rec_model_dir=rec_model_dir
            )
            self.device = "cpu"
            logger.info("PaddleOCR CPU模式初始化成功")

        except Exception as cpu_e:
            logger.error(f"CPU模式也初始化失败: {cpu_e}")
            raise RuntimeError(f"PaddleOCR初始化完全失败 - {self.device}和CPU模式都不可用: {cpu_e}")

    def _init_ocr_cpu_only(self):
        """强制使用CPU模式初始化PaddleOCR"""
        logger.info("强制使用CPU模式初始化PaddleOCR...")

        # 指定本地模型路径
        det_model_dir = "/root/.paddlex/official_models/PP-OCRv5_server_det"
        rec_model_dir = "/root/.paddlex/official_models/PP-OCRv5_server_rec"

        try:
            self.ocr = PaddleOCR(
                use_textline_orientation=True,
                lang='ch',
                device='cpu',
                det_model_dir=det_model_dir,
                rec_model_dir=rec_model_dir
            )
            self.device = "cpu"
            logger.info("强制CPU模式初始化成功")

        except Exception as e:
            logger.error(f"强制CPU模式初始化失败: {e}")
            raise RuntimeError(f"强制CPU模式初始化失败: {e}")
    
    async def recognize_text(self, image_path: str) -> Dict[str, Any]:
        """
        识别图片中的文本
        
        Args:
            image_path: 图片文件路径
            
        Returns:
            识别结果字典
        """
        if not self.initialized:
            raise RuntimeError("OCR服务未初始化")
        
        try:
            logger.info(f"开始识别图片: {image_path}")
            
            # 在异步环境中同步执行OCR识别
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(None, self._recognize_sync, image_path)
            
            logger.info(f"图片识别完成: {image_path}")
            return result
            
        except Exception as e:
            logger.error(f"图片识别失败 {image_path}: {e}")
            raise
    
    def _recognize_sync(self, image_path: str) -> Dict[str, Any]:
        """同步OCR识别方法"""
        try:
            # 使用PaddleOCR进行识别
            # 根据文件类型选择不同的调用方法
            file_ext = os.path.splitext(image_path.lower())[1]

            if file_ext == '.pdf':
                # PDF文件使用ocr方法
                logger.info(f"检测到PDF文件，使用ocr方法处理: {image_path}")
                ocr_result = self.ocr.ocr(image_path)
            else:
                # 图片文件使用predict方法
                logger.info(f"检测到图片文件，使用predict方法处理: {image_path}")
                ocr_result = self.ocr.predict(image_path)
            
            # 解析识别结果
            text_results = []
            total_text = ""

            if ocr_result and isinstance(ocr_result, list) and len(ocr_result) > 0:
                # 判断是predict()还是ocr()的结果格式
                if file_ext == '.pdf' or (isinstance(ocr_result[0], list) and not isinstance(ocr_result[0], dict)):
                    # ocr()方法的结果格式 - 用于PDF或传统格式
                    logger.info("使用ocr()方法结果格式解析")
                    for page_result in ocr_result:
                        if page_result:
                            for line in page_result:
                                if line and len(line) >= 2:
                                    text = line[1][0]
                                    confidence = line[1][1]
                                    bbox = line[0]

                                    text_results.append({
                                        "text": text,
                                        "confidence": float(confidence),
                                        "bbox": bbox
                                    })

                                    total_text += text + "\n"
                else:
                    # predict()方法的结果格式 - 用于图片
                    logger.info("使用predict()方法结果格式解析")
                    result_dict = ocr_result[0] if isinstance(ocr_result[0], dict) else {}

                    # 从结果中提取文本信息
                    if 'rec_texts' in result_dict and 'rec_scores' in result_dict:
                        rec_texts = result_dict['rec_texts']
                        rec_scores = result_dict['rec_scores']
                        rec_boxes = result_dict.get('rec_boxes', [])

                        for i, text in enumerate(rec_texts):
                            confidence = rec_scores[i] if i < len(rec_scores) else 0.0
                            bbox = rec_boxes[i].tolist() if i < len(rec_boxes) else []

                            text_results.append({
                                "text": text,
                                "confidence": float(confidence),
                                "bbox": bbox
                            })

                            total_text += text + "\n"
            
            return {
                "success": True,
                "total_text": total_text.strip(),
                "text_blocks": text_results,
                "total_blocks": len(text_results),
                "device": self.device
            }
            
        except Exception as e:
            logger.error(f"OCR识别过程出错: {e}")
            return {
                "success": False,
                "error": str(e),
                "total_text": "",
                "text_blocks": [],
                "total_blocks": 0,
                "device": self.device
            }
    
    async def check_health(self) -> Dict[str, Any]:
        """检查OCR服务健康状态"""
        try:
            if not self.initialized:
                return {
                    "status": "not_initialized",
                    "device": "unknown",
                    "message": "OCR服务未初始化"
                }
            
            # 创建一个简单的测试图片进行健康检查
            test_image_path = await self._create_test_image()
            
            try:
                result = await self.recognize_text(test_image_path)
                
                return {
                    "status": "healthy" if result["success"] else "unhealthy",
                    "device": self.device,
                    "message": "OCR服务运行正常" if result["success"] else "OCR识别测试失败",
                    "test_result": result
                }
                
            finally:
                # 清理测试图片
                if os.path.exists(test_image_path):
                    os.remove(test_image_path)
                    
        except Exception as e:
            return {
                "status": "unhealthy",
                "device": self.device,
                "message": f"健康检查失败: {str(e)}",
                "error": str(e)
            }
    
    async def _create_test_image(self) -> str:
        """创建测试图片"""
        # 创建一个简单的测试图片
        img = np.ones((100, 300, 3), dtype=np.uint8) * 255
        cv2.putText(img, "Test", (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
        
        # 保存到临时文件
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.png')
        temp_file.close()
        
        cv2.imwrite(temp_file.name, img)
        return temp_file.name 