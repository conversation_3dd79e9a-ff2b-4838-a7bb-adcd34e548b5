from fastapi import FastAPI, File, UploadFile, HTTPException
from fastapi.responses import JSONResponse
import uvicorn
import os
import tempfile
from typing import List, Dict, Any
import logging

from .ocr_service import OCRService
from .utils import save_upload_file, is_valid_file_type

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="OCR RESTful Service",
    description="基于飞桨PaddleOCR的文本识别服务",
    version="1.0.0"
)

# 初始化OCR服务
ocr_service = OCRService()

@app.on_event("startup")
async def startup_event():
    """应用启动时初始化OCR服务"""
    logger.info("正在初始化OCR服务...")
    try:
        await ocr_service.initialize()
        logger.info("OCR服务初始化完成")
    except Exception as e:
        logger.error(f"OCR服务初始化失败: {e}")
        raise

@app.get("/")
async def root():
    """健康检查接口"""
    return {"message": "OCR RESTful Service is running", "status": "healthy"}

@app.get("/health")
async def health_check():
    """详细的健康检查"""
    try:
        ocr_status = await ocr_service.check_health()
        return {
            "status": "healthy",
            "ocr_service": ocr_status,
            "message": "服务运行正常"
        }
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "message": "服务异常"
        }

@app.post("/ocr")
async def ocr_recognize(file: UploadFile = File(...)):
    """OCR文本识别接口"""
    try:
        if not is_valid_file_type(file.filename):
            raise HTTPException(
                status_code=400, 
                detail="不支持的文件类型，仅支持图片(jpg, jpeg, png, bmp)和PDF文件"
            )
        
        temp_file_path = await save_upload_file(file)
        
        try:
            # 直接使用PaddleOCR处理文件（支持图片和PDF）
            result = await ocr_service.recognize_text(temp_file_path)
            
            # 判断文件类型
            file_type = "pdf" if file.filename.lower().endswith('.pdf') else "image"
            
            final_result = {
                "filename": file.filename,
                "file_type": file_type,
                "result": result
            }
            
            logger.info(f"OCR识别完成: {file.filename}")
            return JSONResponse(content=final_result)
            
        finally:
            if os.path.exists(temp_file_path):
                os.remove(temp_file_path)
                
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"OCR识别失败: {e}")
        raise HTTPException(status_code=500, detail=f"OCR识别失败: {str(e)}")

@app.get("/info")
async def get_service_info():
    """获取服务信息"""
    return {
        "service_name": "OCR RESTful Service",
        "version": "1.0.0",
        "framework": "PaddleOCR 3.1.0",
        "device": "NPU",
        "supported_formats": ["jpg", "jpeg", "png", "bmp", "pdf"],
        "endpoints": {
            "ocr": "/ocr (POST)",
            "health": "/health (GET)",
            "info": "/info (GET)"
        }
    }

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=9527) 