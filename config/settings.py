import os
from typing import List

# 应用配置
APP_NAME = "OCR RESTful Service"
APP_VERSION = "1.0.0"
APP_HOST = "0.0.0.0"
APP_PORT = 9527

# 文件上传配置
MAX_FILE_SIZE_MB = 50.0  # 最大文件大小（MB）
SUPPORTED_IMAGE_TYPES = ['.jpg', '.jpeg', '.png', '.bmp']
SUPPORTED_PDF_TYPES = ['.pdf']
SUPPORTED_TYPES = SUPPORTED_IMAGE_TYPES + SUPPORTED_PDF_TYPES

# OCR配置
OCR_DEVICE = "npu:0"  # 默认使用NPU设备
OCR_LANG = "ch"  # 默认中文
OCR_USE_ANGLE_CLS = True  # 使用方向分类器
OCR_USE_GPU = False  # 不使用GPU
OCR_SHOW_LOG = False  # 不显示详细日志

# 日志配置
LOG_LEVEL = "INFO"
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# 临时文件配置
TEMP_DIR = "/tmp/ocr_service"
OUTPUT_DIR = "/app/output"

# 健康检查配置
HEALTH_CHECK_TIMEOUT = 30  # 健康检查超时时间（秒）

# 环境变量配置
def get_env_var(key: str, default=None):
    """获取环境变量"""
    return os.getenv(key, default)

# 从环境变量读取配置
APP_HOST = get_env_var("APP_HOST", APP_HOST)
APP_PORT = int(get_env_var("APP_PORT", APP_PORT))
OCR_DEVICE = get_env_var("OCR_DEVICE", OCR_DEVICE)
MAX_FILE_SIZE_MB = float(get_env_var("MAX_FILE_SIZE_MB", MAX_FILE_SIZE_MB))
LOG_LEVEL = get_env_var("LOG_LEVEL", LOG_LEVEL) 