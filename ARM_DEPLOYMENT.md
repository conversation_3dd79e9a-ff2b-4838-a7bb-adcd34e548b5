# ARM架构部署指南

本指南专门针对ARM架构服务器（昇腾NPU环境）的部署。

## 环境要求

### 硬件要求
- ARM64架构服务器
- 昇腾910B NPU设备
- 至少8GB内存
- 至少50GB可用磁盘空间

### 软件要求
- Ubuntu 20.04 LTS
- Docker 20.10+
- CANN 8.0+
- 昇腾NPU驱动

### 模型文件
- 项目已内置PaddleOCR模型文件（`.paddlex/`目录）
- 支持离线部署，无需联网下载模型

## 环境检查

### 1. 检查系统架构
```bash
uname -m
# 应该显示: aarch64
```

### 2. 检查NPU设备
```bash
# 检查NPU-SMI工具
which npu-smi

# 查看NPU设备信息
npu-smi info

# 检查昇腾驱动
ls /usr/local/Ascend/driver/
```

### 3. 检查Docker
```bash
docker --version
docker info
```

## 部署步骤

### 1. 上传项目文件
将整个项目文件夹上传到ARM服务器：
```bash
# 在本地打包
tar -czf ocr-project.tar.gz *

# 上传到服务器
scp ocr-project.tar.gz user@server:/path/to/deploy/

# 在服务器上解压
tar -xzf ocr-project.tar.gz
cd ocr-project
```

### 2. 构建ARM架构镜像
```bash
# 使用ARM专用构建脚本
./scripts/build.sh

# 或者手动构建
docker build --platform linux/arm64 -t ocr-restful-service:1.0.0 .
```

### 3. 启动服务

#### 方法一：使用ARM专用脚本
```bash
# 启动服务
./scripts/run_arm.sh start

# 查看状态
./scripts/run_arm.sh status

# 查看日志
./scripts/run_arm.sh logs
```

#### 方法二：使用Docker Compose
```bash
# 启动服务
docker-compose up -d

# 查看状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

#### 方法三：手动运行Docker命令
```bash
# 启动容器
docker run -d \
    --name ocr-restful-service \
    --privileged \
    --network=host \
    --shm-size=128G \
    -e ASCEND_RT_VISIBLE_DEVICES="0,1,2,3,4,5,6,7" \
    -v $(pwd)/output:/app/output \
    -v $(pwd)/temp:/app/temp \
    -v /usr/local/Ascend/driver:/usr/local/Ascend/driver \
    -v /usr/local/bin/npu-smi:/usr/local/bin/npu-smi \
    -v /usr/local/dcmi:/usr/local/dcmi \
    ocr-restful-service:1.0.0
```

## 验证部署

### 1. 健康检查
```bash
curl http://localhost:9527/health
```

### 2. 服务信息
```bash
curl http://localhost:9527/info
```

### 3. 测试OCR功能
```bash
# 使用测试客户端
python3 test_client.py

# 或者手动测试
curl -X POST -F "file=@test.jpg" http://localhost:9527/ocr
```

## 离线部署

### 1. 在构建机器上保存镜像
```bash
# 构建镜像
./scripts/build.sh

# 镜像会自动保存为: ocr-restful-service-arm64-1.0.0.tar
```

### 2. 传输镜像文件
```bash
# 将tar文件传输到目标服务器
scp ocr-restful-service-arm64-1.0.0.tar user@server:/path/to/deploy/
```

### 3. 在目标服务器上加载镜像
```bash
# 加载镜像
docker load -i ocr-restful-service-arm64-1.0.0.tar

# 启动服务
./scripts/run_arm.sh start
```

## 配置说明

### 环境变量
- `ASCEND_RT_VISIBLE_DEVICES`: 指定可见的NPU设备ID
- `PYTHONPATH`: Python模块搜索路径
- `LD_PRELOAD`: 预加载库文件

### 挂载目录
- `/usr/local/Ascend/driver`: 昇腾驱动目录
- `/usr/local/bin/npu-smi`: NPU管理工具
- `/usr/local/dcmi`: 设备管理接口
- `./output`: 输出文件目录
- `./temp`: 临时文件目录

### 容器参数
- `--privileged`: 特权模式，访问硬件设备
- `--network=host`: 使用主机网络
- `--shm-size=128G`: 共享内存大小

## 故障排除

### 1. NPU设备不可见
```bash
# 检查NPU设备
npu-smi info

# 检查驱动挂载
ls -la /usr/local/Ascend/driver/

# 检查容器内NPU
docker exec ocr-restful-service npu-smi info
```

### 2. 内存不足
```bash
# 检查内存使用
free -h

# 调整共享内存大小
docker run --shm-size=256G ...
```

### 3. 权限问题
```bash
# 检查Docker权限
sudo usermod -aG docker $USER

# 重新登录
newgrp docker
```

### 4. 网络问题
```bash
# 检查端口占用
netstat -tlnp | grep 9527

# 检查防火墙
sudo ufw status
```

## 性能优化

### 1. NPU配置
```bash
# 设置NPU环境变量
export ASCEND_RT_VISIBLE_DEVICES="0,1,2,3,4,5,6,7"
export ASCEND_DEVICE_ID=0
```

### 2. 内存优化
```bash
# 增加共享内存
docker run --shm-size=256G ...

# 设置内存限制
docker run --memory=16g ...
```

### 3. 存储优化
```bash
# 使用SSD存储
docker run -v /ssd/output:/app/output ...

# 使用tmpfs
docker run --tmpfs /app/temp ...
```

## 监控和维护

### 1. 服务监控
```bash
# 查看服务状态
./scripts/run_arm.sh status

# 查看资源使用
docker stats ocr-restful-service

# 查看NPU使用情况
npu-smi info
```

### 2. 日志管理
```bash
# 查看服务日志
./scripts/run_arm.sh logs

# 查看容器日志
docker logs ocr-restful-service -f
```

### 3. 备份和恢复
```bash
# 备份镜像
docker save -o backup.tar ocr-restful-service:1.0.0

# 恢复镜像
docker load -i backup.tar
```

## 常见问题

### Q: 为什么需要使用ARM专用脚本？
A: ARM架构服务器有特殊的NPU配置要求，专用脚本包含了正确的挂载点和环境变量设置。

### Q: 如何确认NPU加速是否生效？
A: 可以通过以下方式确认：
1. 检查容器内NPU设备：`docker exec ocr-restful-service npu-smi info`
2. 观察OCR处理速度
3. 查看NPU使用率：`npu-smi info`

### Q: 支持哪些文件格式？
A: 支持图片格式（jpg, jpeg, png, bmp）和PDF文件，PaddleOCR原生支持这些格式。

### Q: 如何处理大文件？
A: 可以通过以下方式优化：
1. 增加共享内存：`--shm-size=256G`
2. 调整文件大小限制
3. 使用流式处理

### Q: 是否支持离线部署？
A: 是的，项目已内置PaddleOCR模型文件（`.paddlex/`目录），支持完全离线部署，无需联网下载模型。

### Q: 如何扩展服务？
A: 可以通过以下方式扩展：
1. 使用多个NPU设备
2. 部署多个容器实例
3. 使用负载均衡器 