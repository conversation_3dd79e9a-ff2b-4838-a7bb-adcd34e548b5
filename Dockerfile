# 使用飞桨官方NPU环境镜像
FROM ccr-2vdh3abv-pub.cnc.bj.baidubce.com/device/paddle-npu:cann800-ubuntu20-npu-910b-base-aarch64-gcc84

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    && rm -rf /var/lib/apt/lists/*

# 设置环境变量
ENV PYTHONPATH=/app
ENV LD_PRELOAD=/usr/lib/aarch64-linux-gnu/libgomp.so.1:$LD_PRELOAD

# 复制requirements文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 安装飞桨CPU版本
RUN pip install paddlepaddle==3.1.0 -i https://www.paddlepaddle.org.cn/packages/nightly/cpu


# 安装飞桨NPU插件包
RUN pip install paddle-custom-npu==3.1.0 -i https://www.paddlepaddle.org.cn/packages/nightly/npu

# 卸载numpy并安装指定版本（CANN-8.0.0兼容版本）
RUN pip uninstall -y numpy
RUN pip install numpy==1.26.4

# 安装指定版本的opencv（CANN-8.0.0兼容版本）
RUN pip uninstall -y opencv-python
RUN pip install opencv-python==*********

# 安装PaddleOCR
RUN pip install paddleocr==3.1.0

# 复制应用代码
COPY app/ /app/app/
COPY config/ /app/config/

# 复制PaddleOCR模型文件到容器内
COPY .paddlex/ /root/.paddlex/

# 创建输出目录
RUN mkdir -p /app/output

# 暴露端口
EXPOSE 9527

# 启动命令
CMD ["python", "-m", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "9527"] 